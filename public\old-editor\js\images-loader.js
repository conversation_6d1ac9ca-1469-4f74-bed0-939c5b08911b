// Dynamic Images Loader for Left Sidebar
class ImagesLoader {
    constructor() {
        this.imageGrid = null;
        this.loadedImages = [];
        this.isLoading = false;
    }

    async init() {
        console.log('🖼️ Initializing Images Loader...');

        // Find the image grid container
        this.imageGrid = document.querySelector('#images-sidebar .image-grid');
        if (!this.imageGrid) {
            console.error('Image grid container not found');
            return;
        }

        // Load images when the Images sidebar is opened
        this.setupSidebarListener();

        console.log('🖼️ Images Loader initialized');
    }

    setupSidebarListener() {
        // Listen for when the Images sidebar is opened
        const imagesMenuItem = document.querySelector('[data-sidebar="images-sidebar"]');
        if (imagesMenuItem) {
            imagesMenuItem.addEventListener('click', () => {
                // Small delay to ensure sidebar is open
                setTimeout(() => {
                    if (!this.loadedImages.length && !this.isLoading) {
                        this.loadImages();
                    }
                }, 100);
            });
        }
    }

    async loadImages() {
        if (this.isLoading) return;

        this.isLoading = true;
        console.log('🖼️ Loading images from /stock/images...');

        try {
            // Show loading state
            this.showLoadingState();

            // Fetch the list of images from the server
            const response = await fetch('/api/stock-images');

            if (!response.ok) {
                throw new Error(`Failed to fetch images: ${response.status}`);
            }

            const images = await response.json();
            console.log('🖼️ Received images:', images);

            if (images.length === 0) {
                this.showEmptyState();
                return;
            }

            // Clear loading state and render images
            this.clearGrid();
            this.renderImages(images);
            this.loadedImages = images;

        } catch (error) {
            console.error('🖼️ Error loading images:', error);
            this.showErrorState(error.message);
        } finally {
            this.isLoading = false;
        }
    }

    showLoadingState() {
        this.imageGrid.innerHTML = `
            <div class="loading-state" style="
                grid-column: 1 / -1;
                text-align: center;
                padding: 20px;
                color: #64748b;
                font-size: 0.9em;
            ">
                <div style="margin-bottom: 10px;">📸</div>
                Loading images...
            </div>
        `;
    }

    showEmptyState() {
        this.imageGrid.innerHTML = `
            <div class="empty-state" style="
                grid-column: 1 / -1;
                text-align: center;
                padding: 20px;
                color: #64748b;
                font-size: 0.9em;
            ">
                <div style="margin-bottom: 10px;">📁</div>
                No images found in /stock/images
            </div>
        `;
    }

    showErrorState(message) {
        this.imageGrid.innerHTML = `
            <div class="error-state" style="
                grid-column: 1 / -1;
                text-align: center;
                padding: 20px;
                color: #dc2626;
                font-size: 0.9em;
            ">
                <div style="margin-bottom: 10px;">⚠️</div>
                Error loading images: ${message}
            </div>
        `;
    }

    clearGrid() {
        this.imageGrid.innerHTML = '';
    }

    renderImages(images) {
        images.forEach(imagePath => {
            const imageItem = this.createImageItem(imagePath);
            this.imageGrid.appendChild(imageItem);
        });

        console.log(`🖼️ Rendered ${images.length} images`);
    }

    createImageItem(imagePath) {
        const imageItem = document.createElement('div');
        imageItem.className = 'image-item';
        imageItem.style.cursor = 'pointer';
        imageItem.style.transition = 'transform 0.2s ease, box-shadow 0.2s ease';

        // Create image element
        const img = document.createElement('img');
        img.src = imagePath;
        img.alt = this.getImageName(imagePath);
        img.style.width = '100%';
        img.style.height = '80px';
        img.style.objectFit = 'cover';
        img.style.borderRadius = '4px';
        img.style.border = '1px solid #e2e8f0';

        // Add loading and error handling
        img.addEventListener('load', () => {
            console.log('🖼️ Image loaded:', imagePath);
        });

        img.addEventListener('error', () => {
            console.error('🖼️ Failed to load image:', imagePath);
            img.src = '/images/placeholder.png'; // Fallback to placeholder
            img.alt = 'Failed to load';
        });

        // Add hover effects
        imageItem.addEventListener('mouseenter', () => {
            imageItem.style.transform = 'scale(1.05)';
            imageItem.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });

        imageItem.addEventListener('mouseleave', () => {
            imageItem.style.transform = 'scale(1)';
            imageItem.style.boxShadow = 'none';
        });

        // Add click handler to add image to canvas
        imageItem.addEventListener('click', () => {
            this.addImageToCanvas(imagePath);
        });

        imageItem.appendChild(img);
        return imageItem;
    }

    getImageName(imagePath) {
        return imagePath.split('/').pop().split('.')[0];
    }

    addImageToCanvas(imagePath) {
        console.log('🖼️ Adding image to canvas:', imagePath);

        try {
            // Create a new image object
            const img = new Image();
            img.crossOrigin = 'anonymous'; // Handle CORS if needed

            img.onload = () => {
                // Use the existing handleAddImage function if available
                if (typeof window.addImageToCanvas === 'function') {
                    window.addImageToCanvas(img, imagePath);
                } else {
                    // Fallback: call the design editor's image creation function
                    this.createImageObject(img, imagePath);
                }

                // Show success feedback
                if (window.showToast) {
                    window.showToast('Image added to canvas', 'success');
                }
            };

            img.onerror = () => {
                console.error('🖼️ Failed to load image for canvas:', imagePath);
                if (window.showToast) {
                    window.showToast('Failed to load image', 'error');
                }
            };

            img.src = imagePath;

        } catch (error) {
            console.error('🖼️ Error adding image to canvas:', error);
            if (window.showToast) {
                window.showToast('Error adding image to canvas', 'error');
            }
        }
    }

    createImageObject(img, imagePath) {
        // This function integrates with the existing design editor
        if (typeof window.createImageObject === 'function' &&
            Array.isArray(window.canvasObjects) &&
            typeof window.selectedObjectIndex !== 'undefined' &&
            typeof window.update === 'function') {

            // Get canvas center position
            const canvas = document.getElementById('demo');
            if (!canvas) return;

            const viewCenterX = canvas.clientWidth / 2;
            const viewCenterY = canvas.clientHeight / 2;

            // Convert to world coordinates if canvasToWorld function exists
            let worldCenter = { x: viewCenterX, y: viewCenterY };
            if (typeof window.canvasToWorld === 'function') {
                worldCenter = window.canvasToWorld(viewCenterX, viewCenterY);
            }

            // Create the image object
            const newObj = window.createImageObject(img, {
                x: worldCenter.x + (Math.random() * 40 - 20) / (window.scale || 1),
                y: worldCenter.y + (Math.random() * 40 - 20) / (window.scale || 1),
                imageUrl: imagePath
            });

            // Ensure the object was created successfully
            if (!newObj) {
                console.error('🖼️ Failed to create image object');
                return;
            }

            // Deselect current object safely
            if (window.selectedObjectIndex !== -1 &&
                window.selectedObjectIndex < window.canvasObjects.length &&
                window.canvasObjects[window.selectedObjectIndex]) {
                window.canvasObjects[window.selectedObjectIndex].isSelected = false;
            }

            // Add to canvas and select
            window.canvasObjects.push(newObj);
            window.selectedObjectIndex = window.canvasObjects.length - 1;
            newObj.isSelected = true;

            // Update UI and canvas
            if (typeof window.updateUIFromSelectedObject === 'function') {
                window.updateUIFromSelectedObject();
            }
            window.update();

            // Save state for undo/redo
            if (typeof window.saveState === 'function') {
                window.saveState('Add Stock Image');
            }

            console.log('🖼️ Stock image added to canvas successfully');
        } else {
            console.error('🖼️ Design editor functions not available');
        }
    }
}

// Initialize the images loader when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    const imagesLoader = new ImagesLoader();
    imagesLoader.init();

    // Make it globally available for debugging
    window.imagesLoader = imagesLoader;
});
