// Canvas High-DPI Test - Minimal implementation to debug font rendering
let canvas, ctx, canvasWrapper;
let currentQuality = 1;
let debugLog = [];

// Zoom and pan variables
let zoomLevel = 1;
let panX = 0;
let panY = 0;
let isDragging = false;
let lastMouseX = 0;
let lastMouseY = 0;

// Initialize the test
document.addEventListener('DOMContentLoaded', function() {
    canvas = document.getElementById('testCanvas');
    canvasWrapper = document.getElementById('canvasWrapper');
    ctx = canvas.getContext('2d');

    log('🚀 Canvas test initialized');
    log(`📱 Device pixel ratio: ${window.devicePixelRatio}`);

    setupZoomAndPan();
    setupCanvas();
    updateText();
});

function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    debugLog.push(logMessage);
    
    // Keep only last 50 log entries
    if (debugLog.length > 50) {
        debugLog.shift();
    }
    
    const logElement = document.getElementById('debugLog');
    if (logElement) {
        logElement.innerHTML = 'Debug Log:\n' + debugLog.join('\n');
        logElement.scrollTop = logElement.scrollHeight;
    }
    
    console.log(logMessage);
}

function setQuality(quality) {
    currentQuality = quality;
    
    // Update button states
    document.querySelectorAll('.quality-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    log(`🎯 Quality changed to ${quality}x`);
    setupCanvas();
    updateText();
}

function setupCanvas() {
    const displayWidth = 800;
    const displayHeight = 400;
    const devicePixelRatio = window.devicePixelRatio || 1;
    const totalScale = currentQuality * devicePixelRatio;
    
    log(`🔧 [SETUP] Display size: ${displayWidth}x${displayHeight}`);
    log(`🔧 [SETUP] Device pixel ratio: ${devicePixelRatio}`);
    log(`🔧 [SETUP] Quality multiplier: ${currentQuality}x`);
    log(`🔧 [SETUP] Total scale factor: ${totalScale}`);
    
    // Store the scale factor on the canvas for later use
    canvas.scaleFactor = totalScale;
    
    // Reset any existing transformations
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    log(`🔧 [SETUP] Reset transform to identity`);
    
    // Set the actual canvas size in memory (scaled up for higher quality)
    canvas.width = displayWidth * totalScale;
    canvas.height = displayHeight * totalScale;
    log(`🔧 [SETUP] Canvas memory size: ${canvas.width}x${canvas.height}`);
    
    // Set the display size (CSS size)
    canvas.style.width = displayWidth + 'px';
    canvas.style.height = displayHeight + 'px';
    log(`🔧 [SETUP] Canvas CSS size: ${canvas.style.width} x ${canvas.style.height}`);
    
    // Scale the context to match the device pixel ratio and quality
    ctx.scale(totalScale, totalScale);
    log(`🔧 [SETUP] Applied scale transform: ${totalScale}`);
    log(`🔧 [SETUP] Final transform:`, ctx.getTransform());
    
    // Enable high-quality rendering
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    ctx.textRenderingOptimization = 'optimizeQuality';
    
    updateCanvasInfo();
}

function updateCanvasInfo() {
    const info = document.getElementById('canvasInfo');
    if (!info) return;
    
    const transform = ctx.getTransform();
    
    info.innerHTML = `
        <strong>Canvas Info:</strong><br>
        Memory Size: ${canvas.width} x ${canvas.height}<br>
        CSS Size: ${canvas.style.width} x ${canvas.style.height}<br>
        Scale Factor: ${canvas.scaleFactor}<br>
        Current Quality: ${currentQuality}x<br>
        Device Pixel Ratio: ${window.devicePixelRatio}<br>
        Transform: [${transform.a.toFixed(2)}, ${transform.b.toFixed(2)}, ${transform.c.toFixed(2)}, ${transform.d.toFixed(2)}, ${transform.e.toFixed(2)}, ${transform.f.toFixed(2)}]<br>
        Image Smoothing: ${ctx.imageSmoothingEnabled} (${ctx.imageSmoothingQuality})
    `;
}

function updateText() {
    const fontSize = document.getElementById('fontSize').value;
    const fontFamily = document.getElementById('fontFamily').value;
    const text = document.getElementById('textInput').value;
    const color = document.getElementById('textColor').value;
    const gradientType = document.getElementById('gradientType').value;
    const gradientColor1 = document.getElementById('gradientColor1').value;
    const gradientColor2 = document.getElementById('gradientColor2').value;
    const textMode = document.getElementById('textMode').value;
    const diameter = document.getElementById('diameter').value;
    const kerning = document.getElementById('kerning').value;

    // Update display values
    document.getElementById('fontSizeValue').textContent = fontSize + 'px';
    document.getElementById('diameterValue').textContent = diameter + 'px';
    document.getElementById('kerningValue').textContent = kerning + 'px';

    // Show/hide gradient controls
    const gradientControls = document.getElementById('gradientControls');
    if (gradientType !== 'none') {
        gradientControls.style.display = 'flex';
    } else {
        gradientControls.style.display = 'none';
    }

    // Show/hide circular controls
    const circularControls = document.getElementById('circularControls');
    const circularKerning = document.getElementById('circularKerning');
    if (textMode === 'circular') {
        circularControls.style.display = 'flex';
        circularKerning.style.display = 'flex';
    } else {
        circularControls.style.display = 'none';
        circularKerning.style.display = 'none';
    }

    log(`🔤 [RENDER] Font: ${fontSize}px ${fontFamily}`);
    log(`🔤 [RENDER] Text: "${text}"`);
    log(`🔤 [RENDER] Color: ${color}`);
    log(`🔤 [RENDER] Gradient: ${gradientType} (${gradientColor1} → ${gradientColor2})`);
    log(`🔤 [RENDER] Mode: ${textMode}`);
    if (textMode === 'circular') {
        log(`🔄 [CIRCULAR] Diameter: ${diameter}px, Kerning: ${kerning}px`);
    }

    const gradientConfig = gradientType !== 'none' ? {
        type: gradientType,
        color1: gradientColor1,
        color2: gradientColor2
    } : null;

    renderText(fontSize, fontFamily, text, color, textMode, diameter, kerning, gradientConfig);
}

function renderText(fontSize, fontFamily, text, color, textMode = 'normal', diameter = 300, kerning = 0, gradientConfig = null) {
    // Clear canvas with proper scaling
    ctx.save();

    // CRITICAL: Preserve the high-DPI scaling when clearing
    const scaleFactor = canvas.scaleFactor || 1;
    ctx.setTransform(scaleFactor, 0, 0, scaleFactor, 0, 0);

    // Clear the canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width / scaleFactor, canvas.height / scaleFactor);

    ctx.restore();

    log(`🧹 [CLEAR] Cleared canvas with scale factor: ${scaleFactor}`);
    log(`🧹 [CLEAR] Clear rect: 0,0,${canvas.width / scaleFactor},${canvas.height / scaleFactor}`);

    // Calculate center position (in logical coordinates)
    const centerX = 400; // Half of display width
    const centerY = 200; // Half of display height

    if (gradientConfig) {
        log(`🎨 [GRADIENT] Using gradient masking system`);
        if (textMode === 'circular') {
            renderCircularTextWithGradient(fontSize, fontFamily, text, color, centerX, centerY, diameter, kerning, gradientConfig);
        } else {
            renderNormalTextWithGradient(fontSize, fontFamily, text, color, centerX, centerY, gradientConfig);
        }
    } else {
        log(`🎨 [SOLID] Using solid color rendering`);
        if (textMode === 'circular') {
            renderCircularText(fontSize, fontFamily, text, color, centerX, centerY, diameter, kerning);
        } else {
            renderNormalText(fontSize, fontFamily, text, color, centerX, centerY);
        }
    }

    log(`✅ [COMPLETE] Text rendered successfully`);

    // Update canvas info
    updateCanvasInfo();
}

function renderNormalText(fontSize, fontFamily, text, color, centerX, centerY) {
    // Set font properties
    ctx.font = `${fontSize}px "${fontFamily}"`;
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // Enable high-quality text rendering
    ctx.textRenderingOptimization = 'optimizeQuality';
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    log(`🔤 [FONT] Set font: ${ctx.font}`);
    log(`🔤 [FONT] Current transform:`, ctx.getTransform());
    log(`🔤 [FONT] Image smoothing: ${ctx.imageSmoothingEnabled} (${ctx.imageSmoothingQuality})`);
    log(`🎯 [POSITION] Drawing at: ${centerX}, ${centerY}`);

    // Draw the text
    ctx.fillText(text, centerX, centerY);
}

function renderCircularText(fontSize, fontFamily, text, color, centerX, centerY, diameter, kerning) {
    log(`🔄 [CIRCULAR] Starting circular text rendering`);
    log(`🔄 [CIRCULAR] Parameters: fontSize=${fontSize}, diameter=${diameter}, kerning=${kerning}`);

    const radius = parseInt(diameter) / 2;
    const contentArr = text.split('');
    const letterAngles = [];
    let totalAngle = 0;

    // Calculate letter angles
    ctx.font = `${fontSize}px "${fontFamily}"`;
    contentArr.forEach((letter, i) => {
        const letterWidth = ctx.measureText(letter).width + parseInt(kerning);
        const letterAngle = (letterWidth / radius) * (180 / Math.PI);
        letterAngles.push(letterAngle);
        totalAngle += letterAngle;
        log(`🔄 [CIRCULAR] Letter "${letter}": width=${letterWidth.toFixed(2)}, angle=${letterAngle.toFixed(2)}°`);
    });

    log(`🔄 [CIRCULAR] Total angle: ${totalAngle.toFixed(2)}°`);

    // Start angle to center the text
    let currentAngleRad = -(totalAngle * Math.PI / 180) / 2;

    // Set common properties
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.textRenderingOptimization = 'optimizeQuality';
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    log(`🔄 [CIRCULAR] Starting angle: ${(currentAngleRad * 180 / Math.PI).toFixed(2)}°`);

    // Render each letter
    for (let i = 0; i < contentArr.length; i++) {
        const letter = contentArr[i];
        const letterAngleDeg = letterAngles[i];
        const letterAngleRad = letterAngleDeg * Math.PI / 180;
        const halfAngleRad = letterAngleRad / 2;

        currentAngleRad += halfAngleRad;

        // Calculate position
        const x = centerX + radius * Math.cos(currentAngleRad);
        const y = centerY + radius * Math.sin(currentAngleRad);

        log(`🔄 [CIRCULAR] Letter "${letter}" at position (${x.toFixed(2)}, ${y.toFixed(2)}), angle: ${(currentAngleRad * 180 / Math.PI).toFixed(2)}°`);

        // Save context and apply transforms
        ctx.save();
        ctx.translate(x, y);
        ctx.rotate(currentAngleRad + Math.PI / 2);

        // Draw the letter
        ctx.fillText(letter, 0, 0);

        ctx.restore();

        currentAngleRad += halfAngleRad;
    }

    log(`🔄 [CIRCULAR] Completed circular text rendering`);

    // Debug: Check if pixels were drawn
    const imageData = ctx.getImageData(centerX - radius - 50, centerY - radius - 50, (radius + 50) * 2, (radius + 50) * 2);
    const hasPixels = Array.from(imageData.data).some((value, index) => index % 4 === 3 && value > 0);
    log(`🔄 [CIRCULAR] Pixel check: hasVisiblePixels=${hasPixels}`);
}

// Gradient Masking Functions
function renderNormalTextWithGradient(fontSize, fontFamily, text, color, centerX, centerY, gradientConfig) {
    log(`🎨 [GRADIENT] Starting gradient masking for normal text`);

    const scaleFactor = canvas.scaleFactor || 1;
    const logicalWidth = 600;  // Logical size for text area
    const logicalHeight = 300;

    log(`🎨 [GRADIENT] Scale factor: ${scaleFactor}, logical size: ${logicalWidth}x${logicalHeight}`);

    // Step 1: Create temporary canvas for effects
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = logicalWidth * scaleFactor;
    tempCanvas.height = logicalHeight * scaleFactor;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.scale(scaleFactor, scaleFactor);

    log(`🎨 [GRADIENT] Step 1 - Created temp canvas: ${tempCanvas.width}x${tempCanvas.height}`);

    // Fill with white background and draw text
    tempCtx.fillStyle = '#ffffff';
    tempCtx.fillRect(0, 0, logicalWidth, logicalHeight);

    // Set font and text properties (font size is already scaled by the context scale)
    tempCtx.font = `${fontSize}px "${fontFamily}"`;
    tempCtx.fillStyle = color;
    tempCtx.textAlign = 'center';
    tempCtx.textBaseline = 'middle';
    tempCtx.imageSmoothingEnabled = true;
    tempCtx.imageSmoothingQuality = 'high';
    tempCtx.fillText(text, logicalWidth / 2, logicalHeight / 2);

    // Debug: Check temp canvas (check the full scaled canvas)
    const tempImageData = tempCtx.getImageData(0, 0, tempCanvas.width, tempCanvas.height);
    const tempHasPixels = Array.from(tempImageData.data).some((value, index) => index % 4 === 3 && value > 0);
    log(`🔍 [DEBUG] Temp canvas has visible pixels: ${tempHasPixels}`);

    log(`🎨 [GRADIENT] Step 1 complete - Drew text on temp canvas`);

    // Step 2: Create text mask canvas
    const textCanvas = document.createElement('canvas');
    textCanvas.width = logicalWidth * scaleFactor;
    textCanvas.height = logicalHeight * scaleFactor;
    const textCtx = textCanvas.getContext('2d');
    textCtx.scale(scaleFactor, scaleFactor);

    // Set font and text properties (font size is already scaled by the context scale)
    textCtx.font = `${fontSize}px "${fontFamily}"`;
    textCtx.fillStyle = '#000000';
    textCtx.textAlign = 'center';
    textCtx.textBaseline = 'middle';
    textCtx.imageSmoothingEnabled = true;
    textCtx.imageSmoothingQuality = 'high';
    textCtx.fillText(text, logicalWidth / 2, logicalHeight / 2);

    // Debug: Check text mask (check the full scaled canvas)
    const textImageData = textCtx.getImageData(0, 0, textCanvas.width, textCanvas.height);
    const textHasPixels = Array.from(textImageData.data).some((value, index) => index % 4 === 3 && value > 0);
    log(`🔍 [DEBUG] Text mask has visible pixels: ${textHasPixels}`);

    log(`🎨 [GRADIENT] Step 2 complete - Created text mask`);

    // Step 3: Cut out text from temp canvas
    tempCtx.globalCompositeOperation = 'destination-out';
    tempCtx.drawImage(textCanvas, 0, 0);
    tempCtx.globalCompositeOperation = 'source-over';

    log(`🎨 [GRADIENT] Step 3 complete - Cut out text from temp canvas`);

    // Step 4: Create gradient canvas
    const gradientCanvas = document.createElement('canvas');
    gradientCanvas.width = logicalWidth * scaleFactor;
    gradientCanvas.height = logicalHeight * scaleFactor;
    const gradientCtx = gradientCanvas.getContext('2d');
    gradientCtx.scale(scaleFactor, scaleFactor);

    // Create gradient
    let gradient;
    if (gradientConfig.type === 'linear') {
        gradient = gradientCtx.createLinearGradient(0, 0, logicalWidth, 0);
    } else if (gradientConfig.type === 'radial') {
        gradient = gradientCtx.createRadialGradient(
            logicalWidth / 2, logicalHeight / 2, 0,
            logicalWidth / 2, logicalHeight / 2, Math.min(logicalWidth, logicalHeight) / 2
        );
    }

    gradient.addColorStop(0, gradientConfig.color1);
    gradient.addColorStop(1, gradientConfig.color2);

    gradientCtx.fillStyle = gradient;
    gradientCtx.fillRect(0, 0, logicalWidth, logicalHeight);

    // Mask gradient with text
    gradientCtx.globalCompositeOperation = 'destination-in';
    gradientCtx.drawImage(textCanvas, 0, 0);
    gradientCtx.globalCompositeOperation = 'source-over';

    log(`🎨 [GRADIENT] Step 4 complete - Created gradient text`);

    // Step 5: Draw temp canvas (effects) to main canvas
    // We need to draw the high-res canvas back to the main canvas at the correct position
    const drawX = centerX - logicalWidth / 2;
    const drawY = centerY - logicalHeight / 2;

    ctx.drawImage(tempCanvas, 0, 0, tempCanvas.width, tempCanvas.height,
                  drawX, drawY, logicalWidth, logicalHeight);

    log(`🎨 [GRADIENT] Step 5 complete - Drew effects to main canvas`);

    // Step 6: Draw gradient text to main canvas
    ctx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height,
                  drawX, drawY, logicalWidth, logicalHeight);

    log(`🎨 [GRADIENT] Step 6 complete - Drew gradient text to main canvas`);

    // Debug: Check if gradient canvas has visible pixels (check the full scaled canvas)
    const gradientImageData = gradientCtx.getImageData(0, 0, gradientCanvas.width, gradientCanvas.height);
    const gradientHasPixels = Array.from(gradientImageData.data).some((value, index) => index % 4 === 3 && value > 0);
    log(`🔍 [DEBUG] Gradient canvas has visible pixels: ${gradientHasPixels}`);

    // Debug: Check if main canvas has visible pixels in text area
    const mainImageData = ctx.getImageData(centerX - logicalWidth / 2, centerY - logicalHeight / 2, logicalWidth, logicalHeight);
    const mainHasPixels = Array.from(mainImageData.data).some((value, index) => index % 4 === 3 && value > 0);
    log(`🔍 [DEBUG] Main canvas text area has visible pixels: ${mainHasPixels}`);

    log(`🎨 [GRADIENT] Gradient masking complete!`);
}

function renderCircularTextWithGradient(fontSize, fontFamily, text, color, centerX, centerY, diameter, kerning, gradientConfig) {
    log(`🎨 [GRADIENT] Gradient circular text not implemented yet - falling back to solid color`);
    renderCircularText(fontSize, fontFamily, text, color, centerX, centerY, diameter, kerning);
}

// Zoom and Pan Functions
function setupZoomAndPan() {
    // Mouse wheel zoom
    canvasWrapper.addEventListener('wheel', function(e) {
        e.preventDefault();

        const rect = canvasWrapper.getBoundingClientRect();
        const mouseX = e.clientX - rect.left;
        const mouseY = e.clientY - rect.top;

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
        const newZoom = Math.max(0.1, Math.min(10, zoomLevel * zoomFactor));

        // Calculate zoom center
        const zoomCenterX = (mouseX - panX) / zoomLevel;
        const zoomCenterY = (mouseY - panY) / zoomLevel;

        // Update zoom and adjust pan to keep zoom center in place
        zoomLevel = newZoom;
        panX = mouseX - zoomCenterX * zoomLevel;
        panY = mouseY - zoomCenterY * zoomLevel;

        updateCanvasTransform();
        log(`🔍 [ZOOM] Wheel zoom to ${(zoomLevel * 100).toFixed(0)}%`);
    });

    // Mouse drag pan
    canvasWrapper.addEventListener('mousedown', function(e) {
        isDragging = true;
        lastMouseX = e.clientX;
        lastMouseY = e.clientY;
        canvasWrapper.style.cursor = 'grabbing';
    });

    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        const deltaX = e.clientX - lastMouseX;
        const deltaY = e.clientY - lastMouseY;

        panX += deltaX;
        panY += deltaY;

        lastMouseX = e.clientX;
        lastMouseY = e.clientY;

        updateCanvasTransform();
    });

    document.addEventListener('mouseup', function() {
        isDragging = false;
        canvasWrapper.style.cursor = 'grab';
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.target.tagName === 'INPUT') return;

        switch(e.key) {
            case '+':
            case '=':
                e.preventDefault();
                zoomIn();
                break;
            case '-':
                e.preventDefault();
                zoomOut();
                break;
            case '0':
                e.preventDefault();
                resetZoom();
                break;
            case 'f':
            case 'F':
                e.preventDefault();
                fitToView();
                break;
        }
    });
}

function updateCanvasTransform() {
    canvas.style.transform = `translate(${panX}px, ${panY}px) scale(${zoomLevel})`;
    document.getElementById('zoomInfo').textContent = `${(zoomLevel * 100).toFixed(0)}%`;
}

function zoomIn() {
    zoomLevel = Math.min(10, zoomLevel * 1.2);
    updateCanvasTransform();
    log(`🔍 [ZOOM] Zoom in to ${(zoomLevel * 100).toFixed(0)}%`);
}

function zoomOut() {
    zoomLevel = Math.max(0.1, zoomLevel / 1.2);
    updateCanvasTransform();
    log(`🔍 [ZOOM] Zoom out to ${(zoomLevel * 100).toFixed(0)}%`);
}

function resetZoom() {
    zoomLevel = 1;
    panX = 0;
    panY = 0;
    updateCanvasTransform();
    log(`🔍 [ZOOM] Reset to 100%`);
}

function fitToView() {
    const wrapperRect = canvasWrapper.getBoundingClientRect();
    const canvasRect = canvas.getBoundingClientRect();

    const scaleX = (wrapperRect.width - 40) / canvas.offsetWidth;
    const scaleY = (wrapperRect.height - 40) / canvas.offsetHeight;

    zoomLevel = Math.min(scaleX, scaleY, 1);
    panX = (wrapperRect.width - canvas.offsetWidth * zoomLevel) / 2;
    panY = (wrapperRect.height - canvas.offsetHeight * zoomLevel) / 2;

    updateCanvasTransform();
    log(`🔍 [ZOOM] Fit to view: ${(zoomLevel * 100).toFixed(0)}%`);
}
