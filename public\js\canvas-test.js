// Canvas High-DPI Test - Minimal implementation to debug font rendering
let canvas, ctx;
let currentQuality = 1;
let debugLog = [];

// Initialize the test
document.addEventListener('DOMContentLoaded', function() {
    canvas = document.getElementById('testCanvas');
    ctx = canvas.getContext('2d');
    
    log('🚀 Canvas test initialized');
    log(`📱 Device pixel ratio: ${window.devicePixelRatio}`);
    
    setupCanvas();
    updateText();
});

function log(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;
    debugLog.push(logMessage);
    
    // Keep only last 50 log entries
    if (debugLog.length > 50) {
        debugLog.shift();
    }
    
    const logElement = document.getElementById('debugLog');
    if (logElement) {
        logElement.innerHTML = 'Debug Log:\n' + debugLog.join('\n');
        logElement.scrollTop = logElement.scrollHeight;
    }
    
    console.log(logMessage);
}

function setQuality(quality) {
    currentQuality = quality;
    
    // Update button states
    document.querySelectorAll('.quality-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    log(`🎯 Quality changed to ${quality}x`);
    setupCanvas();
    updateText();
}

function setupCanvas() {
    const displayWidth = 800;
    const displayHeight = 400;
    const devicePixelRatio = window.devicePixelRatio || 1;
    const totalScale = currentQuality * devicePixelRatio;
    
    log(`🔧 [SETUP] Display size: ${displayWidth}x${displayHeight}`);
    log(`🔧 [SETUP] Device pixel ratio: ${devicePixelRatio}`);
    log(`🔧 [SETUP] Quality multiplier: ${currentQuality}x`);
    log(`🔧 [SETUP] Total scale factor: ${totalScale}`);
    
    // Store the scale factor on the canvas for later use
    canvas.scaleFactor = totalScale;
    
    // Reset any existing transformations
    ctx.setTransform(1, 0, 0, 1, 0, 0);
    log(`🔧 [SETUP] Reset transform to identity`);
    
    // Set the actual canvas size in memory (scaled up for higher quality)
    canvas.width = displayWidth * totalScale;
    canvas.height = displayHeight * totalScale;
    log(`🔧 [SETUP] Canvas memory size: ${canvas.width}x${canvas.height}`);
    
    // Set the display size (CSS size)
    canvas.style.width = displayWidth + 'px';
    canvas.style.height = displayHeight + 'px';
    log(`🔧 [SETUP] Canvas CSS size: ${canvas.style.width} x ${canvas.style.height}`);
    
    // Scale the context to match the device pixel ratio and quality
    ctx.scale(totalScale, totalScale);
    log(`🔧 [SETUP] Applied scale transform: ${totalScale}`);
    log(`🔧 [SETUP] Final transform:`, ctx.getTransform());
    
    // Enable high-quality rendering
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    ctx.textRenderingOptimization = 'optimizeQuality';
    
    updateCanvasInfo();
}

function updateCanvasInfo() {
    const info = document.getElementById('canvasInfo');
    if (!info) return;
    
    const transform = ctx.getTransform();
    
    info.innerHTML = `
        <strong>Canvas Info:</strong><br>
        Memory Size: ${canvas.width} x ${canvas.height}<br>
        CSS Size: ${canvas.style.width} x ${canvas.style.height}<br>
        Scale Factor: ${canvas.scaleFactor}<br>
        Current Quality: ${currentQuality}x<br>
        Device Pixel Ratio: ${window.devicePixelRatio}<br>
        Transform: [${transform.a.toFixed(2)}, ${transform.b.toFixed(2)}, ${transform.c.toFixed(2)}, ${transform.d.toFixed(2)}, ${transform.e.toFixed(2)}, ${transform.f.toFixed(2)}]<br>
        Image Smoothing: ${ctx.imageSmoothingEnabled} (${ctx.imageSmoothingQuality})
    `;
}

function updateText() {
    const fontSize = document.getElementById('fontSize').value;
    const fontFamily = document.getElementById('fontFamily').value;
    const text = document.getElementById('textInput').value;
    const color = document.getElementById('textColor').value;
    
    // Update font size display
    document.getElementById('fontSizeValue').textContent = fontSize + 'px';
    
    log(`🔤 [RENDER] Font: ${fontSize}px ${fontFamily}`);
    log(`🔤 [RENDER] Text: "${text}"`);
    log(`🔤 [RENDER] Color: ${color}`);
    
    renderText(fontSize, fontFamily, text, color);
}

function renderText(fontSize, fontFamily, text, color) {
    // Clear canvas with proper scaling
    ctx.save();
    
    // CRITICAL: Preserve the high-DPI scaling when clearing
    const scaleFactor = canvas.scaleFactor || 1;
    ctx.setTransform(scaleFactor, 0, 0, scaleFactor, 0, 0);
    
    // Clear the canvas
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width / scaleFactor, canvas.height / scaleFactor);
    
    ctx.restore();
    
    log(`🧹 [CLEAR] Cleared canvas with scale factor: ${scaleFactor}`);
    log(`🧹 [CLEAR] Clear rect: 0,0,${canvas.width / scaleFactor},${canvas.height / scaleFactor}`);
    
    // Set font properties
    ctx.font = `${fontSize}px "${fontFamily}"`;
    ctx.fillStyle = color;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    
    // Enable high-quality text rendering
    ctx.textRenderingOptimization = 'optimizeQuality';
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';
    
    log(`🔤 [FONT] Set font: ${ctx.font}`);
    log(`🔤 [FONT] Current transform:`, ctx.getTransform());
    log(`🔤 [FONT] Image smoothing: ${ctx.imageSmoothingEnabled} (${ctx.imageSmoothingQuality})`);
    
    // Calculate center position (in logical coordinates)
    const centerX = 400; // Half of display width
    const centerY = 200; // Half of display height
    
    log(`🎯 [POSITION] Drawing at: ${centerX}, ${centerY}`);
    
    // Draw the text
    ctx.fillText(text, centerX, centerY);
    
    log(`✅ [COMPLETE] Text rendered successfully`);
    
    // Update canvas info
    updateCanvasInfo();
}
