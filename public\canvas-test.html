<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas High-DPI Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quality-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .quality-btn {
            padding: 10px 20px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .quality-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .font-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .font-control {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .font-control label {
            font-weight: bold;
            font-size: 12px;
        }
        
        .font-control input, .font-control select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .canvas-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .canvas-wrapper {
            position: relative;
            display: inline-block;
            cursor: grab;
            overflow: hidden;
            border: 1px solid #ddd;
            background: white;
        }

        .canvas-wrapper:active {
            cursor: grabbing;
        }

        #testCanvas {
            display: block;
            transform-origin: 0 0;
            transition: transform 0.1s ease-out;
        }

        .zoom-controls {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
        }

        .zoom-btn {
            padding: 8px 15px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }

        .zoom-btn:hover {
            background: #f8f9fa;
        }

        .zoom-info {
            font-weight: bold;
            color: #007bff;
            min-width: 80px;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
        }
        
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas High-DPI Font Rendering Test</h1>
        
        <div class="controls">
            <div class="quality-buttons">
                <button class="quality-btn active" onclick="setQuality(1)">1x Quality</button>
                <button class="quality-btn" onclick="setQuality(2)">2x Quality</button>
                <button class="quality-btn" onclick="setQuality(4)">4x Quality</button>
            </div>
            
            <div class="font-controls">
                <div class="font-control">
                    <label>Font Size</label>
                    <input type="range" id="fontSize" min="12" max="200" value="80" oninput="updateText()">
                    <span id="fontSizeValue">80px</span>
                </div>
                
                <div class="font-control">
                    <label>Font Family</label>
                    <select id="fontFamily" onchange="updateText()">
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Poppins">Poppins</option>
                    </select>
                </div>
                
                <div class="font-control">
                    <label>Text</label>
                    <input type="text" id="textInput" value="DESIGN" oninput="updateText()">
                </div>
                
                <div class="font-control">
                    <label>Color</label>
                    <input type="color" id="textColor" value="#3b82f6" onchange="updateText()">
                </div>

                <div class="font-control">
                    <label>Gradient</label>
                    <select id="gradientType" onchange="updateText()">
                        <option value="none">No Gradient</option>
                        <option value="linear">Linear Gradient</option>
                        <option value="radial">Radial Gradient</option>
                    </select>
                </div>

                <div class="font-control" id="gradientControls" style="display: none;">
                    <label>Gradient Colors</label>
                    <div style="display: flex; gap: 5px;">
                        <input type="color" id="gradientColor1" value="#ff0000" onchange="updateText()">
                        <input type="color" id="gradientColor2" value="#0000ff" onchange="updateText()">
                    </div>
                </div>

                <div class="font-control">
                    <label>Text Mode</label>
                    <select id="textMode" onchange="updateText()">
                        <option value="normal">Normal</option>
                        <option value="circular">Circular</option>
                    </select>
                </div>

                <div class="font-control" id="circularControls" style="display: none;">
                    <label>Diameter</label>
                    <input type="range" id="diameter" min="100" max="600" value="300" oninput="updateText()">
                    <span id="diameterValue">300px</span>
                </div>

                <div class="font-control" id="circularKerning" style="display: none;">
                    <label>Kerning</label>
                    <input type="range" id="kerning" min="-20" max="50" value="0" oninput="updateText()">
                    <span id="kerningValue">0px</span>
                </div>
            </div>
        </div>
        
        <div class="canvas-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomOut()">Zoom Out (-)</button>
                <span class="zoom-info" id="zoomInfo">100%</span>
                <button class="zoom-btn" onclick="zoomIn()">Zoom In (+)</button>
                <button class="zoom-btn" onclick="resetZoom()">Reset</button>
                <button class="zoom-btn" onclick="fitToView()">Fit to View</button>
            </div>

            <div class="canvas-wrapper" id="canvasWrapper">
                <canvas id="testCanvas" width="800" height="400"></canvas>
            </div>
            
            <div class="info" id="canvasInfo">
                Canvas Info: Loading...
            </div>
            
            <div class="log" id="debugLog">
                Debug Log:
            </div>
        </div>
    </div>

    <script src="/js/canvas-test.js?v=1751197006000"></script>
</body>
</html>
