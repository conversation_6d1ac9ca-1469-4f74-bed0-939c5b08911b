<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas High-DPI Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .controls {
            margin-bottom: 20px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .quality-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .quality-btn {
            padding: 10px 20px;
            border: 2px solid #ddd;
            background: white;
            border-radius: 5px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .quality-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }
        
        .font-controls {
            display: flex;
            gap: 20px;
            align-items: center;
            flex-wrap: wrap;
        }
        
        .font-control {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        
        .font-control label {
            font-weight: bold;
            font-size: 12px;
        }
        
        .font-control input, .font-control select {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .canvas-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }
        
        #testCanvas {
            border: 1px solid #ddd;
            background: white;
        }
        
        .info {
            margin-top: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            text-align: left;
        }
        
        .log {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 11px;
            text-align: left;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Canvas High-DPI Font Rendering Test</h1>
        
        <div class="controls">
            <div class="quality-buttons">
                <button class="quality-btn active" onclick="setQuality(1)">1x Quality</button>
                <button class="quality-btn" onclick="setQuality(2)">2x Quality</button>
                <button class="quality-btn" onclick="setQuality(4)">4x Quality</button>
            </div>
            
            <div class="font-controls">
                <div class="font-control">
                    <label>Font Size</label>
                    <input type="range" id="fontSize" min="12" max="200" value="80" oninput="updateText()">
                    <span id="fontSizeValue">80px</span>
                </div>
                
                <div class="font-control">
                    <label>Font Family</label>
                    <select id="fontFamily" onchange="updateText()">
                        <option value="Arial">Arial</option>
                        <option value="Helvetica">Helvetica</option>
                        <option value="Times New Roman">Times New Roman</option>
                        <option value="Georgia">Georgia</option>
                        <option value="Verdana">Verdana</option>
                        <option value="Poppins">Poppins</option>
                    </select>
                </div>
                
                <div class="font-control">
                    <label>Text</label>
                    <input type="text" id="textInput" value="DESIGN" oninput="updateText()">
                </div>
                
                <div class="font-control">
                    <label>Color</label>
                    <input type="color" id="textColor" value="#3b82f6" onchange="updateText()">
                </div>
            </div>
        </div>
        
        <div class="canvas-container">
            <canvas id="testCanvas" width="800" height="400"></canvas>
            
            <div class="info" id="canvasInfo">
                Canvas Info: Loading...
            </div>
            
            <div class="log" id="debugLog">
                Debug Log:
            </div>
        </div>
    </div>

    <script src="/js/canvas-test.js?v=1751197005000"></script>
</body>
</html>
