<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>High-DPI Comparison - Before vs After</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .canvas-panel {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            text-align: center;
        }
        
        .canvas-panel h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        
        .canvas-panel canvas {
            border: 1px solid #ddd;
            background: white;
            max-width: 100%;
            height: auto;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .status.good {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.bad {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .controls {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .controls h3 {
            margin: 0 0 15px 0;
        }
        
        .control-group {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .control-group label {
            min-width: 100px;
            font-weight: bold;
        }
        
        .control-group input, .control-group select {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        
        .zoom-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: center;
            margin-top: 15px;
        }
        
        .zoom-btn {
            padding: 8px 15px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .zoom-btn:hover {
            background: #f8f9fa;
        }
        
        .zoom-info {
            font-weight: bold;
            color: #007bff;
            min-width: 80px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 High-DPI Canvas Comparison</h1>
        <p>Compare the old low-DPI rendering vs the new high-DPI rendering. Zoom in to see the difference!</p>
        
        <div class="controls">
            <h3>Test Controls</h3>
            <div class="control-group">
                <label>Text:</label>
                <input type="text" id="textInput" value="DESIGN" placeholder="Enter text">
            </div>
            <div class="control-group">
                <label>Font Size:</label>
                <input type="range" id="fontSize" min="20" max="200" value="80">
                <span id="fontSizeValue">80px</span>
            </div>
            <div class="control-group">
                <label>Font Family:</label>
                <select id="fontFamily">
                    <option value="Arial">Arial</option>
                    <option value="Helvetica">Helvetica</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Georgia">Georgia</option>
                    <option value="Verdana">Verdana</option>
                </select>
            </div>
            <div class="control-group">
                <label>Color:</label>
                <input type="color" id="textColor" value="#3b82f6">
            </div>
        </div>
        
        <div class="comparison-grid">
            <div class="canvas-panel">
                <h3>❌ Old Method (Low-DPI)</h3>
                <canvas id="oldCanvas" width="400" height="200"></canvas>
                <div class="status bad">
                    <strong>Issues:</strong> Blurry text, jagged edges, poor quality on high-DPI displays
                </div>
            </div>
            
            <div class="canvas-panel">
                <h3>✅ New Method (High-DPI)</h3>
                <canvas id="newCanvas" width="400" height="200"></canvas>
                <div class="status good">
                    <strong>Benefits:</strong> Crisp text, smooth edges, excellent quality on all displays
                </div>
            </div>
        </div>
        
        <div class="zoom-controls">
            <button class="zoom-btn" onclick="zoomOut()">Zoom Out (-)</button>
            <span class="zoom-info" id="zoomInfo">100%</span>
            <button class="zoom-btn" onclick="zoomIn()">Zoom In (+)</button>
            <button class="zoom-btn" onclick="resetZoom()">Reset</button>
        </div>
    </div>

    <script>
        let oldCanvas, oldCtx, newCanvas, newCtx;
        let zoomLevel = 1;
        
        // Initialize canvases
        document.addEventListener('DOMContentLoaded', function() {
            oldCanvas = document.getElementById('oldCanvas');
            oldCtx = oldCanvas.getContext('2d');
            newCanvas = document.getElementById('newCanvas');
            newCtx = newCanvas.getContext('2d');
            
            setupNewCanvas();
            updateText();
            
            // Event listeners
            document.getElementById('textInput').addEventListener('input', updateText);
            document.getElementById('fontSize').addEventListener('input', updateText);
            document.getElementById('fontFamily').addEventListener('change', updateText);
            document.getElementById('textColor').addEventListener('change', updateText);
        });
        
        function setupNewCanvas() {
            const displayWidth = 400;
            const displayHeight = 200;
            const devicePixelRatio = window.devicePixelRatio || 1;
            const qualityMultiplier = 2; // 2x quality
            const totalScale = devicePixelRatio * qualityMultiplier;
            
            // Store scale factor
            newCanvas.scaleFactor = totalScale;
            
            // Set memory size (high resolution)
            newCanvas.width = displayWidth * totalScale;
            newCanvas.height = displayHeight * totalScale;
            
            // Set display size (CSS)
            newCanvas.style.width = displayWidth + 'px';
            newCanvas.style.height = displayHeight + 'px';
            
            // Apply scaling
            newCtx.scale(totalScale, totalScale);
            
            // Enable high quality
            newCtx.imageSmoothingEnabled = true;
            newCtx.imageSmoothingQuality = 'high';
            newCtx.textRenderingOptimization = 'optimizeQuality';
        }
        
        function updateText() {
            const text = document.getElementById('textInput').value;
            const fontSize = document.getElementById('fontSize').value;
            const fontFamily = document.getElementById('fontFamily').value;
            const color = document.getElementById('textColor').value;
            
            document.getElementById('fontSizeValue').textContent = fontSize + 'px';
            
            // Render old method (low-DPI)
            renderOldMethod(text, fontSize, fontFamily, color);
            
            // Render new method (high-DPI)
            renderNewMethod(text, fontSize, fontFamily, color);
        }
        
        function renderOldMethod(text, fontSize, fontFamily, color) {
            // Clear normally (this destroys high-DPI scaling)
            oldCtx.clearRect(0, 0, oldCanvas.width, oldCanvas.height);
            
            // Fill background
            oldCtx.fillStyle = '#ffffff';
            oldCtx.fillRect(0, 0, oldCanvas.width, oldCanvas.height);
            
            // Set font and render
            oldCtx.font = `${fontSize}px "${fontFamily}"`;
            oldCtx.fillStyle = color;
            oldCtx.textAlign = 'center';
            oldCtx.textBaseline = 'middle';
            
            oldCtx.fillText(text, oldCanvas.width / 2, oldCanvas.height / 2);
        }
        
        function renderNewMethod(text, fontSize, fontFamily, color) {
            // Clear with high-DPI preservation
            newCtx.save();
            
            const scaleFactor = newCanvas.scaleFactor || 1;
            newCtx.setTransform(scaleFactor, 0, 0, scaleFactor, 0, 0);
            
            // Clear and fill background
            newCtx.fillStyle = '#ffffff';
            newCtx.fillRect(0, 0, newCanvas.width / scaleFactor, newCanvas.height / scaleFactor);
            
            newCtx.restore();
            
            // Set font and render
            newCtx.font = `${fontSize}px "${fontFamily}"`;
            newCtx.fillStyle = color;
            newCtx.textAlign = 'center';
            newCtx.textBaseline = 'middle';
            
            newCtx.fillText(text, 200, 100); // Logical coordinates
        }
        
        function updateZoom() {
            const containers = document.querySelectorAll('.canvas-panel');
            containers.forEach(container => {
                container.style.transform = `scale(${zoomLevel})`;
                container.style.transformOrigin = 'center';
            });
            document.getElementById('zoomInfo').textContent = `${(zoomLevel * 100).toFixed(0)}%`;
        }
        
        function zoomIn() {
            zoomLevel = Math.min(5, zoomLevel * 1.2);
            updateZoom();
        }
        
        function zoomOut() {
            zoomLevel = Math.max(0.5, zoomLevel / 1.2);
            updateZoom();
        }
        
        function resetZoom() {
            zoomLevel = 1;
            updateZoom();
        }
    </script>
</body>
</html>
