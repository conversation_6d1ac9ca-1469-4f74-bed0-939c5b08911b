/* Filerobot Image Editor Bundle */
/* This file implements a simplified but functional version of the Filerobot Image Editor */

(function() {
  // Define constants used in the editor
  const TABS = {
    ADJUST: 'Adjust',
    FINETUNE: 'Finetune',
    FILTERS: 'Filter',
    WATERMARK: 'Watermark',
    ANNOTATE: 'Annotate',
    RESIZE: 'Resize'
  };
  
  const TOOLS = {
    CROP: 'Crop',
    ROTATE: 'Rotate',
    FLIP_X: 'FlipX',
    FLIP_Y: 'FlipY',
    BRIGHTNESS: 'Brightness',
    CONTRAST: 'Contrast',
    HUE: 'Hue',
    SATURATION: 'Saturation',
    BLUR: 'Blur',
    SHADOW: 'Shadow',
    TEXT: 'Text',
    RESIZE: 'Resize'
  };

  // Core image processing functions
  const ImageProcessing = {
    applyFilter(canvas, filterId, value = 1) {
      const ctx = canvas.getContext('2d');
      const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
      const data = imageData.data;
      
      switch(filterId) {
        case 'blackwhite':
          for (let i = 0; i < data.length; i += 4) {
            const avg = (data[i] + data[i + 1] + data[i + 2]) / 3;
            data[i] = data[i + 1] = data[i + 2] = avg;
          }
          break;
        case 'sepia':
          for (let i = 0; i < data.length; i += 4) {
            const r = data[i];
            const g = data[i + 1];
            const b = data[i + 2];
            data[i] = Math.min(255, (r * 0.393) + (g * 0.769) + (b * 0.189));
            data[i + 1] = Math.min(255, (r * 0.349) + (g * 0.686) + (b * 0.168));
            data[i + 2] = Math.min(255, (r * 0.272) + (g * 0.534) + (b * 0.131));
          }
          break;
        case 'clarity':
          // Simple sharpening effect
          const tempCanvas = document.createElement('canvas');
          tempCanvas.width = canvas.width;
          tempCanvas.height = canvas.height;
          const tempCtx = tempCanvas.getContext('2d');
          tempCtx.putImageData(imageData, 0, 0);
          
          // Apply slight blur first
          ctx.filter = 'blur(1px)';
          ctx.drawImage(canvas, 0, 0);
          ctx.filter = 'none';
          
          // Then overlay original with reduced opacity
          ctx.globalAlpha = 0.5;
          ctx.drawImage(tempCanvas, 0, 0);
          ctx.globalAlpha = 1.0;
          
          return; // Skip the putImageData below as we've already modified the canvas
          
        case 'brightness':
          const factor = 1 + value * 0.01;
          for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, data[i] * factor);
            data[i + 1] = Math.min(255, data[i + 1] * factor);
            data[i + 2] = Math.min(255, data[i + 2] * factor);
          }
          break;
        case 'contrast':
          const contrastFactor = (259 * (value + 255)) / (255 * (259 - value));
          for (let i = 0; i < data.length; i += 4) {
            data[i] = Math.min(255, Math.max(0, contrastFactor * (data[i] - 128) + 128));
            data[i + 1] = Math.min(255, Math.max(0, contrastFactor * (data[i + 1] - 128) + 128));
            data[i + 2] = Math.min(255, Math.max(0, contrastFactor * (data[i + 2] - 128) + 128));
          }
          break;
      }
      
      ctx.putImageData(imageData, 0, 0);
    },
    
    rotate(canvas, degrees) {
      const ctx = canvas.getContext('2d');
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      
      tempCanvas.width = canvas.width;
      tempCanvas.height = canvas.height;
      tempCtx.drawImage(canvas, 0, 0);
      
      // Clear canvas and set new dimensions if needed (for 90/270 degrees)
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      if (degrees === 90 || degrees === 270) {
        const temp = canvas.width;
        canvas.width = canvas.height;
        canvas.height = temp;
      }
      
      // Translate and rotate
      ctx.save();
      ctx.translate(canvas.width / 2, canvas.height / 2);
      ctx.rotate((degrees * Math.PI) / 180);
      ctx.drawImage(tempCanvas, -tempCanvas.width / 2, -tempCanvas.height / 2);
      ctx.restore();
    },
    
    flipX(canvas) {
      const ctx = canvas.getContext('2d');
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      
      tempCanvas.width = canvas.width;
      tempCanvas.height = canvas.height;
      tempCtx.drawImage(canvas, 0, 0);
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.translate(canvas.width, 0);
      ctx.scale(-1, 1);
      ctx.drawImage(tempCanvas, 0, 0);
      ctx.setTransform(1, 0, 0, 1, 0, 0);
    },
    
    flipY(canvas) {
      const ctx = canvas.getContext('2d');
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      
      tempCanvas.width = canvas.width;
      tempCanvas.height = canvas.height;
      tempCtx.drawImage(canvas, 0, 0);
      
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      ctx.translate(0, canvas.height);
      ctx.scale(1, -1);
      ctx.drawImage(tempCanvas, 0, 0);
      ctx.setTransform(1, 0, 0, 1, 0, 0);
    },
    
    crop(canvas, cropData) {
      if (!this.imageLoaded || !canvas) return;
      
      // Save current state to history
      this.saveToHistory();
      
      const ctx = canvas.getContext('2d');
      
      // Create temporary canvas with cropped portion
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = cropData.width;
      tempCanvas.height = cropData.height;
      const tempCtx = tempCanvas.getContext('2d');
      
      // Draw the cropped portion to the temp canvas
      tempCtx.drawImage(
        canvas, 
        cropData.x, cropData.y, cropData.width, cropData.height,
        0, 0, cropData.width, cropData.height
      );
      
      // Resize original canvas to the crop dimensions
      canvas.width = cropData.width;
      canvas.height = cropData.height;
      
      // Draw the temp canvas back to the original
      ctx.drawImage(tempCanvas, 0, 0);
      
      return this;
    },
    
    resize(canvas, dimensions) {
      const { width, height } = dimensions;
      const ctx = canvas.getContext('2d');
      const tempCanvas = document.createElement('canvas');
      const tempCtx = tempCanvas.getContext('2d');
      
      tempCanvas.width = canvas.width;
      tempCanvas.height = canvas.height;
      tempCtx.drawImage(canvas, 0, 0);
      
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(tempCanvas, 0, 0, tempCanvas.width, tempCanvas.height, 0, 0, width, height);
    }
  };

  // Main editor class
  class FilerobotImageEditor {
    constructor(container, config = {}) {
      if (!container) {
        console.error('Container element is required for FilerobotImageEditor');
        throw new Error('Container element is required for FilerobotImageEditor');
      }
      
      this.container = container;
      this.config = this.validateConfig(config);
      this.currentTab = TABS.ADJUST;
      this.currentTool = TOOLS.CROP;
      this.originalImage = null;
      this.canvas = null;
      this.imageLoaded = false;
      this.state = {
        cropData: { x: 0, y: 0, width: 0, height: 0 },
        filters: {}
      };
      
      // History tracking
      this.history = [];
      this.historyIndex = -1;
      
      // Initialize editor UI
      this.initUI();
    }
    
    validateConfig(config) {
      // Set default source if not provided
      if (!config.source) {
        console.warn('No source image provided, using default placeholder');
        config.source = './images/placeholder.png';
      }
      
      return config;
    }
    
    initUI() {
      try {
        this.container.innerHTML = '';
        this.container.classList.add('fie-container');
        
        // Create editor layout
        this.createEditorLayout();
        
        // Load initial image if provided
        if (this.config.source) {
          this.loadImage(this.config.source);
        } else {
          // If no source provided, show empty state with upload prompt
          const emptyState = document.createElement('div');
          emptyState.className = 'fie-empty-state';
          emptyState.innerHTML = `
            <div class="fie-empty-message">
              <p>No image loaded. Please upload an image to edit.</p>
              <button class="fie-upload-btn">Upload Image</button>
            </div>
          `;
          
          const uploadBtn = emptyState.querySelector('.fie-upload-btn');
          if (uploadBtn) {
            uploadBtn.addEventListener('click', () => {
              const fileInput = document.createElement('input');
              fileInput.type = 'file';
              fileInput.accept = 'image/*';
              fileInput.onchange = (event) => {
                const file = event.target.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (e) => {
                    this.loadImage(e.target.result);
                  };
                  reader.readAsDataURL(file);
                }
              };
              fileInput.click();
            });
          }
          
          this.container.appendChild(emptyState);
        }
      } catch (error) {
        console.error('Error initializing UI:', error);
        this.container.innerHTML = `<div class="fie-error">Error initializing editor: ${error.message}</div>`;
      }
    }
    
    createEditorLayout() {
      // Create main layout elements
      const layout = document.createElement('div');
      layout.className = 'fie-layout';
      
      // Create edit controls toolbar
      const editControls = document.createElement('div');
      editControls.className = 'fie-edit-controls';
      
      // Create undo button
      const undoBtn = document.createElement('button');
      undoBtn.className = 'fie-toolbar-btn fie-undo-btn';
      undoBtn.title = 'Undo';
      undoBtn.innerHTML = '<span>↶</span>';
      undoBtn.disabled = true;
      undoBtn.addEventListener('click', () => this.undo());
      editControls.appendChild(undoBtn);
      
      // Create redo button
      const redoBtn = document.createElement('button');
      redoBtn.className = 'fie-toolbar-btn fie-redo-btn';
      redoBtn.title = 'Redo';
      redoBtn.innerHTML = '<span>↷</span>';
      redoBtn.disabled = true;
      redoBtn.addEventListener('click', () => this.redo());
      editControls.appendChild(redoBtn);
      
      // Create separator
      const separator = document.createElement('div');
      separator.className = 'fie-toolbar-separator';
      editControls.appendChild(separator);
      
      // Save button
      const saveBtn = document.createElement('button');
      saveBtn.className = 'fie-toolbar-btn fie-save-btn';
      saveBtn.title = 'Save Image';
      saveBtn.innerHTML = '<span>💾</span>';
      saveBtn.disabled = !this.imageLoaded;
      saveBtn.addEventListener('click', () => this.saveImage());
      editControls.appendChild(saveBtn);
      
      // Reset button
      const resetBtn = document.createElement('button');
      resetBtn.className = 'fie-toolbar-btn fie-reset-btn';
      resetBtn.title = 'Reset Image';
      resetBtn.innerHTML = '<span>⟲</span>';
      resetBtn.disabled = !this.imageLoaded;
      resetBtn.addEventListener('click', () => this.resetImage());
      editControls.appendChild(resetBtn);
      
      // Add edit controls to layout
      layout.appendChild(editControls);
      
      // Create tab toolbar
      const toolbar = document.createElement('div');
      toolbar.className = 'fie-toolbar';
      
      // Filter tabs based on config.tabsIds if provided
      let tabsToShow = Object.values(TABS);
      if (this.config.tabsIds && Array.isArray(this.config.tabsIds)) {
        // Convert all tab IDs to lowercase for case-insensitive comparison
        const configTabsLower = this.config.tabsIds.map(tab => 
          typeof tab === 'string' ? tab.toLowerCase() : tab);
        
        tabsToShow = Object.values(TABS).filter(tab => 
          configTabsLower.includes(tab.toLowerCase()));
        
        // If no valid tabs, show all
        if (tabsToShow.length === 0) {
          console.warn('No valid tabs found in config.tabsIds, showing all tabs');
          tabsToShow = Object.values(TABS);
        }
      }
      
      console.log('Showing tabs:', tabsToShow);
      
      // Add tab buttons
      tabsToShow.forEach(tab => {
        const tabButton = document.createElement('button');
        tabButton.className = 'fie-tab-button';
        tabButton.textContent = tab;
        tabButton.addEventListener('click', () => this.switchTab(tab));
        
        // Set active class for default tab
        if (this.config.defaultTabId && 
            tab.toLowerCase() === this.config.defaultTabId.toLowerCase()) {
          tabButton.classList.add('active');
          this.currentTab = tab;
        } else if (!this.config.defaultTabId && tab === TABS.ADJUST) {
          // If no default tab specified, use ADJUST
          tabButton.classList.add('active');
          this.currentTab = tab;
        }
        
        toolbar.appendChild(tabButton);
      });
      
      layout.appendChild(toolbar);
      
      // Create canvas container
      const canvasContainer = document.createElement('div');
      canvasContainer.className = 'fie-canvas-container';
      
      // Create canvas element
      this.canvas = document.createElement('canvas');
      this.canvas.className = 'fie-canvas';
      this.canvas.width = 600;
      this.canvas.height = 400;
      
      canvasContainer.appendChild(this.canvas);
      layout.appendChild(canvasContainer);
      
      // Create tools container
      const toolsContainer = document.createElement('div');
      toolsContainer.className = 'fie-tools-container';
      layout.appendChild(toolsContainer);
      
      // Create controls container for each tab
      tabsToShow.forEach(tab => {
        const controlsContainer = document.createElement('div');
        controlsContainer.className = `fie-tab-controls fie-${tab.toLowerCase()}-controls`;
        controlsContainer.style.display = 'none'; // Hide initially
        
        toolsContainer.appendChild(controlsContainer);
      });
      
      this.container.appendChild(layout);
      
      // Initialize quick effects section
      this.initQuickEffects();
      
      // Show controls for default tab
      this.showControlsForTab(this.currentTab);
    }
    
    createTabControls(container) {
      // Create controls for each tab
      const adjustControls = this.createAdjustControls();
      adjustControls.className = 'fie-tab-controls fie-adjust-controls';
      
      const finetuneControls = this.createFinetuneControls();
      finetuneControls.className = 'fie-tab-controls fie-finetune-controls';
      
      const filtersControls = this.createFiltersControls();
      filtersControls.className = 'fie-tab-controls fie-filters-controls';
      
      const watermarkControls = this.createWatermarkControls();
      watermarkControls.className = 'fie-tab-controls fie-watermark-controls';
      
      const annotateControls = this.createAnnotateControls();
      annotateControls.className = 'fie-tab-controls fie-annotate-controls';
      
      const resizeControls = this.createResizeControls();
      resizeControls.className = 'fie-tab-controls fie-resize-controls';
      
      // Add all controls to container
      container.appendChild(adjustControls);
      container.appendChild(finetuneControls);
      container.appendChild(filtersControls);
      container.appendChild(watermarkControls);
      container.appendChild(annotateControls);
      container.appendChild(resizeControls);
      
      // Show only the default tab's controls
      this.showControlsForTab(this.currentTab);
    }
    
    showControlsForTab(tabId) {
      if (!tabId) {
        console.warn('No tab ID provided to showControlsForTab');
        return;
      }
      
      console.log('Showing controls for tab:', tabId);
      
      // Normalize tab ID for consistent handling
      const normalizedTabId = tabId.toLowerCase();
      
      // Hide all controls
      const allControls = this.container.querySelectorAll('.fie-tab-controls');
      allControls.forEach(control => {
        control.style.display = 'none';
        console.log('Hiding control:', control.className);
      });
      
      // Show controls for current tab
      const controlClass = `fie-${normalizedTabId}-controls`;
      console.log('Looking for control with class:', controlClass);
      
      let control = this.container.querySelector(`.${controlClass}`);
      
      // If control doesn't exist yet, create it
      if (!control) {
        console.log('Control not found, creating for tab:', normalizedTabId);
        
        // Create container for controls
        control = document.createElement('div');
        control.className = `fie-tab-controls ${controlClass}`;
        
        // Create specific controls based on tab
        let tabControls = null;
        
        if (normalizedTabId === 'adjust') {
          tabControls = this.createAdjustControls();
        } else if (normalizedTabId === 'finetune') {
          tabControls = this.createFinetuneControls();
        } else if (normalizedTabId === 'filter' || normalizedTabId === 'filters') {
          tabControls = this.createFilterControls();
        } else if (normalizedTabId === 'watermark') {
          tabControls = this.createWatermarkControls();
        } else if (normalizedTabId === 'annotate') {
          tabControls = this.createAnnotateControls();
        } else if (normalizedTabId === 'resize') {
          tabControls = this.createResizeControls();
        }
        
        if (tabControls) {
          control.appendChild(tabControls);
          // Add to container
          const toolsContainer = this.container.querySelector('.fie-tools-container');
          if (toolsContainer) {
            toolsContainer.appendChild(control);
          } else {
            this.container.appendChild(control);
          }
        }
      }
      
      // Show the control
      if (control) {
        console.log('Showing control for tab:', normalizedTabId);
        control.style.display = 'block';
      } else {
        console.warn(`No controls found for tab: ${normalizedTabId}`);
      }
    }
    
    createAdjustControls() {
      const controls = document.createElement('div');
      
      // Crop tool
      const cropSection = document.createElement('div');
      cropSection.className = 'fie-control-section';
      
      const cropTitle = document.createElement('h3');
      cropTitle.textContent = 'Crop';
      cropSection.appendChild(cropTitle);
      
      const cropBtn = document.createElement('button');
      cropBtn.className = 'fie-tool-btn';
      cropBtn.textContent = 'Start Crop';
      cropBtn.addEventListener('click', () => {
        this.switchTool(TOOLS.CROP);
        this.startCropMode();
      });
      cropSection.appendChild(cropBtn);
      
      controls.appendChild(cropSection);
      
      // Rotate tool
      const rotateSection = document.createElement('div');
      rotateSection.className = 'fie-control-section';
      
      const rotateTitle = document.createElement('h3');
      rotateTitle.textContent = 'Rotate';
      rotateSection.appendChild(rotateTitle);
      
      const rotateLeftBtn = document.createElement('button');
      rotateLeftBtn.className = 'fie-tool-btn';
      rotateLeftBtn.textContent = 'Rotate Left';
      rotateLeftBtn.addEventListener('click', () => {
        this.rotate(-90);
      });
      
      const rotateRightBtn = document.createElement('button');
      rotateRightBtn.className = 'fie-tool-btn';
      rotateRightBtn.textContent = 'Rotate Right';
      rotateRightBtn.addEventListener('click', () => {
        this.rotate(90);
      });
      
      rotateSection.appendChild(rotateLeftBtn);
      rotateSection.appendChild(rotateRightBtn);
      controls.appendChild(rotateSection);
      
      // Flip tool
      const flipSection = document.createElement('div');
      flipSection.className = 'fie-control-section';
      
      const flipTitle = document.createElement('h3');
      flipTitle.textContent = 'Flip';
      flipSection.appendChild(flipTitle);
      
      const flipXBtn = document.createElement('button');
      flipXBtn.className = 'fie-tool-btn';
      flipXBtn.textContent = 'Flip Horizontal';
      flipXBtn.addEventListener('click', () => {
        this.flipImage('x');
      });
      
      const flipYBtn = document.createElement('button');
      flipYBtn.className = 'fie-tool-btn';
      flipYBtn.textContent = 'Flip Vertical';
      flipYBtn.addEventListener('click', () => {
        this.flipImage('y');
      });
      
      flipSection.appendChild(flipXBtn);
      flipSection.appendChild(flipYBtn);
      controls.appendChild(flipSection);
      
      return controls;
    }
    
    createFinetuneControls() {
      const controls = document.createElement('div');
      
      // Brightness
      this.createSliderControl(
        controls, 
        'Brightness', 
        -100, 
        100, 
        0, 
        (value) => this.applyFilter('brightness', value)
      );
      
      // Contrast
      this.createSliderControl(
        controls, 
        'Contrast', 
        -100, 
        100, 
        0, 
        (value) => this.applyFilter('contrast', value)
      );
      
      return controls;
    }
    
    createFiltersControls() {
      const controls = document.createElement('div');
      
      const filters = [
        { id: 'original', name: 'Original' },
        { id: 'blackwhite', name: 'Black & White' },
        { id: 'sepia', name: 'Sepia' },
        { id: 'clarity', name: 'Clarity' }
      ];
      
      const filtersGrid = document.createElement('div');
      filtersGrid.className = 'fie-filters-grid';
      
      filters.forEach(filter => {
        const filterBtn = document.createElement('div');
        filterBtn.className = 'fie-filter-item';
        filterBtn.textContent = filter.name;
        
        filterBtn.addEventListener('click', () => {
          if (filter.id === 'original') {
            this.reset();
          } else {
            this.applyFilter(filter.id);
          }
          
          // Mark as selected
          const allFilters = filtersGrid.querySelectorAll('.fie-filter-item');
          allFilters.forEach(item => item.classList.remove('active'));
          filterBtn.classList.add('active');
        });
        
        filtersGrid.appendChild(filterBtn);
      });
      
      controls.appendChild(filtersGrid);
      
      return controls;
    }
    
    createWatermarkControls() {
      const controls = document.createElement('div');
      
      const textInput = document.createElement('input');
      textInput.type = 'text';
      textInput.placeholder = 'Watermark text';
      textInput.className = 'fie-text-input';
      
      const colorInput = document.createElement('input');
      colorInput.type = 'color';
      colorInput.value = '#ff0000';
      colorInput.className = 'fie-color-input';
      
      const sizeSlider = document.createElement('input');
      sizeSlider.type = 'range';
      sizeSlider.min = '10';
      sizeSlider.max = '100';
      sizeSlider.value = '30';
      sizeSlider.className = 'fie-slider-input';
      
      const applyBtn = document.createElement('button');
      applyBtn.className = 'fie-btn';
      applyBtn.textContent = 'Apply Watermark';
      applyBtn.addEventListener('click', () => {
        this.applyWatermark(textInput.value, colorInput.value, parseInt(sizeSlider.value));
      });
      
      controls.appendChild(textInput);
      controls.appendChild(colorInput);
      controls.appendChild(sizeSlider);
      controls.appendChild(applyBtn);
      
      return controls;
    }
    
    createAnnotateControls() {
      console.log('Creating annotation controls');
      
      const controls = document.createElement('div');
      controls.className = 'fie-annotate-controls fie-controls-section';
      
      // Text input section
      const textSection = document.createElement('div');
      textSection.className = 'fie-control-section';
      
      const textHeader = document.createElement('h3');
      textHeader.textContent = 'Text Annotation';
      textSection.appendChild(textHeader);
      
      // Text input field
      const textInputContainer = document.createElement('div');
      textInputContainer.className = 'fie-input-container';
      
      const textInput = document.createElement('input');
      textInput.type = 'text';
      textInput.placeholder = 'Enter text...';
      textInput.className = 'fie-text-input';
      textInputContainer.appendChild(textInput);
      
      textSection.appendChild(textInputContainer);
      
      // Text style controls
      const styleControls = document.createElement('div');
      styleControls.className = 'fie-style-controls';
      
      // Font size slider
      const fontSizeContainer = document.createElement('div');
      fontSizeContainer.className = 'fie-slider-container';
      
      const fontSizeLabel = document.createElement('label');
      fontSizeLabel.textContent = 'Font Size';
      fontSizeContainer.appendChild(fontSizeLabel);
      
      const fontSizeSlider = document.createElement('input');
      fontSizeSlider.type = 'range';
      fontSizeSlider.min = '12';
      fontSizeSlider.max = '72';
      fontSizeSlider.value = '24';
      fontSizeSlider.className = 'fie-slider';
      fontSizeContainer.appendChild(fontSizeSlider);
      
      const fontSizeValue = document.createElement('span');
      fontSizeValue.textContent = '24';
      fontSizeValue.className = 'fie-slider-value';
      fontSizeContainer.appendChild(fontSizeValue);
      
      // Update font size value when slider changes
      fontSizeSlider.addEventListener('input', () => {
        fontSizeValue.textContent = fontSizeSlider.value;
      });
      
      styleControls.appendChild(fontSizeContainer);
      
      // Font selection
      const fontSelectContainer = document.createElement('div');
      fontSelectContainer.className = 'fie-select-container';
      
      const fontLabel = document.createElement('label');
      fontLabel.textContent = 'Font';
      fontSelectContainer.appendChild(fontLabel);
      
      const fontSelect = document.createElement('select');
      fontSelect.className = 'fie-select';
      
      // Add common fonts
      const fonts = [
        'Arial', 
        'Verdana', 
        'Helvetica', 
        'Times New Roman', 
        'Courier New', 
        'Georgia', 
        'Palatino', 
        'Garamond', 
        'Comic Sans MS', 
        'Impact'
      ];
      
      fonts.forEach(font => {
        const option = document.createElement('option');
        option.value = font;
        option.textContent = font;
        option.style.fontFamily = font;
        fontSelect.appendChild(option);
      });
      
      fontSelectContainer.appendChild(fontSelect);
      styleControls.appendChild(fontSelectContainer);
      
      // Text color picker
      const colorContainer = document.createElement('div');
      colorContainer.className = 'fie-color-container';
      
      const colorLabel = document.createElement('label');
      colorLabel.textContent = 'Text Color';
      colorContainer.appendChild(colorLabel);
      
      const colorInput = document.createElement('input');
      colorInput.type = 'color';
      colorInput.value = '#ff0000';
      colorInput.className = 'fie-color-input';
      colorContainer.appendChild(colorInput);
      
      styleControls.appendChild(colorContainer);
      
      // Font style buttons
      const fontStyleContainer = document.createElement('div');
      fontStyleContainer.className = 'fie-font-style-container';
      
      // Bold button
      const boldBtn = document.createElement('button');
      boldBtn.className = 'fie-style-btn';
      boldBtn.innerHTML = '<strong>B</strong>';
      boldBtn.title = 'Bold';
      boldBtn.dataset.active = 'false';
      boldBtn.addEventListener('click', () => {
        boldBtn.dataset.active = boldBtn.dataset.active === 'true' ? 'false' : 'true';
        boldBtn.classList.toggle('active');
      });
      fontStyleContainer.appendChild(boldBtn);
      
      // Italic button
      const italicBtn = document.createElement('button');
      italicBtn.className = 'fie-style-btn';
      italicBtn.innerHTML = '<em>I</em>';
      italicBtn.title = 'Italic';
      italicBtn.dataset.active = 'false';
      italicBtn.addEventListener('click', () => {
        italicBtn.dataset.active = italicBtn.dataset.active === 'true' ? 'false' : 'true';
        italicBtn.classList.toggle('active');
      });
      fontStyleContainer.appendChild(italicBtn);
      
      styleControls.appendChild(fontStyleContainer);
      
      // Add style controls to the text section
      textSection.appendChild(styleControls);
      
      // Button to add text
      const addTextBtn = document.createElement('button');
      addTextBtn.className = 'fie-btn fie-primary-btn';
      addTextBtn.textContent = 'Add Text';
      addTextBtn.addEventListener('click', () => {
        console.log('Add text button clicked with:', {
          text: textInput.value,
          color: colorInput.value,
          fontSize: fontSizeSlider.value,
          fontFamily: fontSelect.value,
          isBold: boldBtn.dataset.active === 'true',
          isItalic: italicBtn.dataset.active === 'true'
        });
        
        // Call addText with all the options
        this.addText(
          textInput.value, 
          colorInput.value, 
          parseInt(fontSizeSlider.value), 
          fontSelect.value,
          boldBtn.dataset.active === 'true',
          italicBtn.dataset.active === 'true'
        );
      });
      
      textSection.appendChild(addTextBtn);
      
      // Add the text section to controls
      controls.appendChild(textSection);
      
      // Add a note about placement
      const note = document.createElement('div');
      note.className = 'fie-note';
      note.innerHTML = '<small>Text will be added to the center of the image. You can adjust position in a future version.</small>';
      controls.appendChild(note);
      
      return controls;
    }
    
    createResizeControls() {
      const controls = document.createElement('div');
      
      const widthInput = document.createElement('input');
      widthInput.type = 'number';
      widthInput.placeholder = 'Width';
      widthInput.className = 'fie-number-input';
      
      const heightInput = document.createElement('input');
      heightInput.type = 'number';
      heightInput.placeholder = 'Height';
      heightInput.className = 'fie-number-input';
      
      const maintainRatio = document.createElement('input');
      maintainRatio.type = 'checkbox';
      maintainRatio.checked = true;
      
      const resizeBtn = document.createElement('button');
      resizeBtn.className = 'fie-btn';
      resizeBtn.textContent = 'Resize Image';
      resizeBtn.addEventListener('click', () => {
        this.resize({
          width: parseInt(widthInput.value),
          height: parseInt(heightInput.value)
        });
      });
      
      // Set initial values
      if (this.canvas) {
        widthInput.value = this.canvas.width;
        heightInput.value = this.canvas.height;
        
        // Update ratio
        const aspectRatio = this.canvas.width / this.canvas.height;
        
        widthInput.addEventListener('change', () => {
          if (maintainRatio.checked) {
            heightInput.value = Math.round(widthInput.value / aspectRatio);
          }
        });
        
        heightInput.addEventListener('change', () => {
          if (maintainRatio.checked) {
            widthInput.value = Math.round(heightInput.value * aspectRatio);
          }
        });
      }
      
      const ratioLabel = document.createElement('label');
      ratioLabel.innerHTML = '<span>Maintain aspect ratio</span>';
      ratioLabel.prepend(maintainRatio);
      
      controls.appendChild(widthInput);
      controls.appendChild(heightInput);
      controls.appendChild(ratioLabel);
      controls.appendChild(resizeBtn);
      
      return controls;
    }
    
    initQuickEffects() {
      console.log('Initializing quick effects section');
      
      // Create quick effects section
      const quickEffectsSection = document.createElement('div');
      quickEffectsSection.className = 'fie-quick-effects-section';
      
      const quickEffectsTitle = document.createElement('div');
      quickEffectsTitle.className = 'fie-section-title';
      quickEffectsTitle.textContent = 'Quick Effects';
      quickEffectsSection.appendChild(quickEffectsTitle);
      
      // Create quick effects buttons
      const effectsGrid = document.createElement('div');
      effectsGrid.className = 'fie-effects-grid';
      
      // B&W Effect
      const bwEffect = document.createElement('button');
      bwEffect.className = 'fie-effect-btn';
      bwEffect.textContent = 'B&W';
      bwEffect.addEventListener('click', () => {
        console.log('Applying B&W filter');
        this.applyFilter('blackAndWhite');
      });
      
      // Sepia Effect
      const sepiaEffect = document.createElement('button');
      sepiaEffect.className = 'fie-effect-btn';
      sepiaEffect.textContent = 'Sepia';
      sepiaEffect.addEventListener('click', () => {
        console.log('Applying Sepia filter');
        this.applyFilter('sepia');
      });
      
      // Clarity Effect
      const clarityEffect = document.createElement('button');
      clarityEffect.className = 'fie-effect-btn';
      clarityEffect.textContent = 'Clarity';
      clarityEffect.addEventListener('click', () => {
        console.log('Applying Clarity filter');
        this.applyFilter('clarity');
      });
      
      effectsGrid.appendChild(bwEffect);
      effectsGrid.appendChild(sepiaEffect);
      effectsGrid.appendChild(clarityEffect);
      
      quickEffectsSection.appendChild(effectsGrid);
      
      // Add to container
      const layout = this.container.querySelector('.fie-layout');
      if (layout) {
        layout.appendChild(quickEffectsSection);
      }
    }
    
    createSliderControl(parent, labelText, min, max, defaultValue, onChange) {
      const controlSection = document.createElement('div');
      controlSection.className = 'fie-control-section';
      
      const label = document.createElement('h3');
      label.textContent = labelText;
      
      const slider = document.createElement('input');
      slider.type = 'range';
      slider.min = min;
      slider.max = max;
      slider.value = defaultValue;
      slider.className = 'fie-slider-input';
      
      const valueDisplay = document.createElement('span');
      valueDisplay.textContent = defaultValue;
      valueDisplay.className = 'fie-slider-value';
      
      slider.addEventListener('input', (e) => {
        const value = parseInt(e.target.value);
        valueDisplay.textContent = value;
        onChange(value);
      });
      
      controlSection.appendChild(label);
      controlSection.appendChild(slider);
      controlSection.appendChild(valueDisplay);
      
      parent.appendChild(controlSection);
      
      return controlSection;
    }
    
    render(additionalConfig = {}) {
      if (additionalConfig) {
        this.config = { ...this.config, ...additionalConfig };
      }
      
      // Reinitialize UI with updated config
      this.initUI();
      
      return this;
    }
    
    loadImage(imageSource) {
      if (!imageSource) {
        console.error('Image source is required');
        return;
      }
      
      // Clear any existing image
      if (this.canvas) {
        const ctx = this.canvas.getContext('2d');
        ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      }
      
      // Create a new Image object
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      // Set up load and error handlers
      img.onload = () => {
        this.onImageLoad(img);
      };
      
      img.onerror = (e) => {
        this.onImageError(e);
      };
      
      // Start loading the image
      img.src = imageSource;
    }
    
    onImageLoad(img) {
      console.log('Image loaded successfully!');
      
      const sourceWidth = img.width;
      const sourceHeight = img.height;
      console.log('Original image dimensions:', sourceWidth, 'x', sourceHeight);
      
      // Determine canvas dimensions based on container size or image size
      let canvasWidth, canvasHeight;
      
      if (this.options && this.options.fixedSize) {
        canvasWidth = this.options.width || sourceWidth;
        canvasHeight = this.options.height || sourceHeight;
      } else {
        const containerWidth = this.container.clientWidth * 0.8;
        const containerHeight = this.container.clientHeight * 0.8;
        
        const widthRatio = containerWidth / sourceWidth;
        const heightRatio = containerHeight / sourceHeight;
        
        const scaleFactor = Math.min(widthRatio, heightRatio, 1);
        
        canvasWidth = sourceWidth * scaleFactor;
        canvasHeight = sourceHeight * scaleFactor;
      }
      
      // Update canvas dimensions
      this.canvas.width = canvasWidth;
      this.canvas.height = canvasHeight;
      console.log('Canvas dimensions set to:', canvasWidth, 'x', canvasHeight);
      
      // Draw image on canvas
      const ctx = this.canvas.getContext('2d');
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      ctx.drawImage(img, 0, 0, sourceWidth, sourceHeight, 0, 0, canvasWidth, canvasHeight);
      
      // Set image loaded flag
      this.imageLoaded = true;
      this.originalImage = img;
      
      // Initialize history
      this.initHistory();
      
      // Show the canvas and hide loader
      if (this.canvas.parentElement) {
        this.canvas.style.display = 'block';
      }
      
      const loaderElement = this.container.querySelector('.fie-loader');
      if (loaderElement) {
        loaderElement.style.display = 'none';
      }
      
      // Update UI elements
      this.updateUI();
      
      console.log('Image rendered on canvas');
    }
    
    onImageError(error) {
      console.error('Error loading image:', error);
      
      // Get the failed image source
      const imgSrc = error.target?.src || '';
      console.log('Failed image source:', imgSrc);
      
      // Try loading a fallback image if the original fails
      if (imgSrc && !imgSrc.includes('error-placeholder.png')) {
        console.log('Attempting to load fallback image...');
        // Use a more robust path that accounts for different environments
        const fallbackPath = window.location.origin + '/images/error-placeholder.png';
        console.log('Fallback image path:', fallbackPath);
        this.loadImage(fallbackPath);
      } else {
        // If we're already trying to load the fallback and it fails, show error message
        this.container.innerHTML = '<div class="fie-error">Error loading image. Please try a different image.</div>';
      }
    }
    
    switchTab(tabId) {
      if (!this.imageLoaded) return this;
      
      // Validate tab ID
      const validTabs = Object.values(TABS);
      const normalizedTabId = tabId.charAt(0).toUpperCase() + tabId.slice(1).toLowerCase();
      
      if (!validTabs.includes(normalizedTabId)) {
        console.warn(`Invalid tab ID: ${tabId}`);
        return this;
      }
      
      // Update current tab
      this.currentTab = normalizedTabId;
      
      // Update tab button states
      const tabButtons = this.container.querySelectorAll('.fie-tab-button');
      tabButtons.forEach(button => {
        if (button.textContent === normalizedTabId) {
          button.classList.add('active');
        } else {
          button.classList.remove('active');
        }
      });
      
      // Show corresponding controls
      this.showControlsForTab(normalizedTabId);
      
      return this;
    }
    
    switchTool(toolId) {
      if (!this.imageLoaded) return this;
      
      // Validate tool ID
      const validTools = Object.values(TOOLS);
      const normalizedToolId = toolId.charAt(0).toUpperCase() + toolId.slice(1).toLowerCase();
      
      if (!validTools.includes(normalizedToolId)) {
        console.warn(`Invalid tool ID: ${toolId}`);
        return this;
      }
      
      // Update current tool
      this.currentTool = normalizedToolId;
      
      // If switching to crop tool, start crop mode
      if (normalizedToolId === TOOLS.CROP) {
        this.startCropMode();
      }
      
      return this;
    }
    
    applyFilter(filterId, value) {
      if (!this.imageLoaded) return this;
      
      // Save current state to history before applying filter
      this.saveToHistory();
      
      // Apply filter to canvas
      ImageProcessing.applyFilter(this.canvas, filterId, value);
      
      // Store filter state
      this.state.filters[filterId] = value || true;
      
      return this;
    }
    
    rotate(degrees) {
      if (!this.imageLoaded) return this;
      
      // Save current state to history
      this.saveToHistory();
      
      ImageProcessing.rotate(this.canvas, degrees);
      return this;
    }
    
    flipImage(direction) {
      if (!this.imageLoaded) return this;
      
      // Save current state to history
      this.saveToHistory();
      
      if (direction === 'x') {
        ImageProcessing.flipX(this.canvas);
      } else if (direction === 'y') {
        ImageProcessing.flipY(this.canvas);
      }
      
      return this;
    }
    
    crop(cropData) {
      if (!this.imageLoaded || !this.canvas) return;
      
      // Save current state to history
      this.saveToHistory();
      
      const ctx = this.canvas.getContext('2d');
      
      // Create temporary canvas with cropped portion
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = cropData.width;
      tempCanvas.height = cropData.height;
      const tempCtx = tempCanvas.getContext('2d');
      
      // Draw the cropped portion to the temp canvas
      tempCtx.drawImage(
        this.canvas, 
        cropData.x, cropData.y, cropData.width, cropData.height,
        0, 0, cropData.width, cropData.height
      );
      
      // Resize original canvas to the crop dimensions
      this.canvas.width = cropData.width;
      this.canvas.height = cropData.height;
      
      // Draw the temp canvas back to the original
      ctx.drawImage(tempCanvas, 0, 0);
      
      return this;
    }
    
    resize(dimensions) {
      if (!this.imageLoaded) return this;
      
      ImageProcessing.resize(this.canvas, dimensions);
      return this;
    }
    
    saveImage() {
      if (!this.canvas || !this.imageLoaded) return this;
      
      console.log('Saving image...');
      
      // Create a download link
      const link = document.createElement('a');
      
      // Get the canvas image data
      const imageData = this.canvas.toDataURL('image/png');
      
      // Set link attributes
      link.href = imageData;
      link.download = 'edited-image.png';
      
      // Append link to document
      document.body.appendChild(link);
      
      // Trigger click event
      link.click();
      
      // Remove link from document
      document.body.removeChild(link);
      
      // Mark image as not modified since it's now saved
      this.imageModified = false;
      
      // Update UI buttons
      this.updateUI();
      
      console.log('Image saved');
      
      return this;
    }
    
    resetImage() {
      if (!this.imageLoaded || !this.originalImage) return this;
      
      console.log('Resetting image to original state');
      
      // Push current state to history before resetting
      this.pushHistory('Before reset');
      
      // Clear canvas
      const ctx = this.canvas.getContext('2d');
      ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      
      // Reset canvas dimensions to match original image or container constraints
      if (this.options && this.options.fixedSize) {
        // Maintain fixed size if specified
        this.canvas.width = this.options.width || this.originalImage.width;
        this.canvas.height = this.options.height || this.originalImage.height;
      } else {
        // Adjust based on container
        const containerWidth = this.container.clientWidth * 0.8;
        const containerHeight = this.container.clientHeight * 0.8;
        
        const widthRatio = containerWidth / this.originalImage.width;
        const heightRatio = containerHeight / this.originalImage.height;
        
        const scaleFactor = Math.min(widthRatio, heightRatio, 1);
        
        this.canvas.width = this.originalImage.width * scaleFactor;
        this.canvas.height = this.originalImage.height * scaleFactor;
      }
      
      // Draw the original image back to the canvas with proper scaling
      ctx.drawImage(
        this.originalImage, 
        0, 0, this.originalImage.width, this.originalImage.height,
        0, 0, this.canvas.width, this.canvas.height
      );
      
      // Reset all state parameters
      this.state = {
        cropData: { x: 0, y: 0, width: 0, height: 0 },
        filters: {}
      };
      
      // Push the reset state to history
      this.pushHistory('Reset to original');
      
      // Update UI
      this.updateUI();
      
      console.log('Image reset complete');
      
      return this;
    }
    
    reset() {
      if (!this.imageLoaded || !this.originalImage) return this;
      
      // Save current state to history
      this.saveToHistory();
      
      // Reset canvas dimensions to match the original image
      this.canvas.width = this.originalImage.width;
      this.canvas.height = this.originalImage.height;
      
      // Clear canvas
      const ctx = this.canvas.getContext('2d');
      ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
      
      // Draw the original image back to the canvas
      ctx.drawImage(this.originalImage, 0, 0);
      
      // Reset all states
      this.state = {
        cropData: { x: 0, y: 0, width: 0, height: 0 },
        filters: {}
      };
      
      return this;
    }
    
    terminate() {
      // Clean up resources
      this.container.innerHTML = '';
      this.canvas = null;
      this.originalImage = null;
      this.imageLoaded = false;
      
      return this;
    }
    
    // History management methods
    pushHistory(action) {
      if (!this.canvas) return;
      
      // Get current canvas state
      const dataURL = this.canvas.toDataURL();
      
      // Add state to history
      this.history.push({
        dataURL,
        action,
        timestamp: Date.now()
      });
      
      // Set history index to latest
      this.historyIndex = this.history.length - 1;
      
      // Clear redo stack if new action performed
      this.redoStack = [];
      
      // Keep history at reasonable size
      if (this.history.length > 20) { // Limit to 20 states
        this.history.shift();
        this.historyIndex--;
      }
      
      // Update UI buttons
      this.updateUI();
    }
    
    // Alias for pushHistory for backward compatibility
    saveToHistory(action) {
      // If action is not provided, use a default message
      const actionText = action || 'Action';
      this.pushHistory(actionText);
    }
    
    undo() {
      if (!this.history || this.history.length <= 1) return;
      
      // Get current state and push to redo stack
      const currentState = this.history.pop();
      this.redoStack.push(currentState);
      
      // Apply previous state
      const previousState = this.history[this.history.length - 1];
      this.applyHistoryState(previousState);
      
      // Update UI buttons
      const undoButton = document.querySelector('.fie-undo-btn');
      if (undoButton) {
        undoButton.disabled = this.history.length <= 1;
      }
      
      const redoButton = document.querySelector('.fie-redo-btn');
      if (redoButton) {
        redoButton.disabled = false;
      }
      
      console.log('Undo performed, history length:', this.history.length);
    }
    
    redo() {
      if (!this.redoStack || this.redoStack.length === 0) return;
      
      // Get state from redo stack
      const redoState = this.redoStack.pop();
      
      // Push to history
      this.history.push(redoState);
      
      // Apply state
      this.applyHistoryState(redoState);
      
      // Update UI buttons
      const undoButton = document.querySelector('.fie-undo-btn');
      if (undoButton) {
        undoButton.disabled = false;
      }
      
      const redoButton = document.querySelector('.fie-redo-btn');
      if (redoButton) {
        redoButton.disabled = this.redoStack.length === 0;
      }
      
      console.log('Redo performed, redo stack length:', this.redoStack.length);
    }
    
    applyHistoryState(historyItem) {
      if (!historyItem || !historyItem.dataURL) return;
      
      // Create a new image to load the history state
      const img = new Image();
      img.onload = () => {
        // Clear canvas
        const ctx = this.canvas.getContext('2d');
        ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw the saved state
        ctx.drawImage(img, 0, 0, this.canvas.width, this.canvas.height);
        
        console.log('Applied history state:', historyItem.action);
      };
      
      // Set source to history state
      img.src = historyItem.dataURL;
    }
    
    // Initialize history
    initHistory() {
      // Create initial history entry
      if (this.canvas && this.imageLoaded) {
        this.history = [];
        this.redoStack = [];
        
        // Push initial state
        this.pushHistory('Initial state');
      }
    }
    
    // Crop functionality
    startCropMode() {
      if (!this.canvas || !this.imageLoaded) return;
      
      // Save current state to history
      this.saveToHistory();
      
      // Create crop overlay
      const overlay = document.createElement('div');
      overlay.className = 'fie-crop-overlay';
      
      // Create crop selection
      const selection = document.createElement('div');
      selection.className = 'fie-crop-selection';
      
      // Initial crop rectangle is centered 50% of image
      const initialWidth = this.canvas.width * 0.5;
      const initialHeight = this.canvas.height * 0.5;
      
      selection.style.width = `${initialWidth}px`;
      selection.style.height = `${initialHeight}px`;
      selection.style.left = `${(this.canvas.width - initialWidth) / 2}px`;
      selection.style.top = `${(this.canvas.height - initialHeight) / 2}px`;
      
      // Create resize handles
      const handles = ['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'];
      handles.forEach(position => {
        const handle = document.createElement('div');
        handle.className = `fie-crop-handle fie-crop-handle-${position}`;
        selection.appendChild(handle);
      });
      
      overlay.appendChild(selection);
      
      // Add buttons
      const cropControls = document.createElement('div');
      cropControls.className = 'fie-crop-controls';
      
      const applyCropBtn = document.createElement('button');
      applyCropBtn.className = 'fie-btn fie-apply-crop-btn';
      applyCropBtn.textContent = 'Apply Crop';
      
      const cancelCropBtn = document.createElement('button');
      cancelCropBtn.className = 'fie-btn fie-cancel-crop-btn';
      cancelCropBtn.textContent = 'Cancel';
      
      cropControls.appendChild(applyCropBtn);
      cropControls.appendChild(cancelCropBtn);
      
      overlay.appendChild(cropControls);
      
      // Add overlay to canvas container
      const canvasContainer = this.container.querySelector('.fie-canvas-container');
      if (canvasContainer) {
        canvasContainer.appendChild(overlay);
      }
      
      // Setup drag & resize functionality
      this.setupCropInteractions(selection, overlay);
      
      // Setup buttons
      applyCropBtn.addEventListener('click', () => {
        this.applyCrop(selection);
        this.exitCropMode();
      });
      
      cancelCropBtn.addEventListener('click', () => {
        this.exitCropMode();
      });
    }
    
    setupCropInteractions(selection, overlay) {
      let isDragging = false;
      let isResizing = false;
      let currentHandle = null;
      let startX, startY, startWidth, startHeight, startLeft, startTop;
      
      // Helper to get selected area bounds relative to canvas
      const getSelectionBounds = () => {
        const canvasRect = this.canvas.getBoundingClientRect();
        const selectionRect = selection.getBoundingClientRect();
        
        return {
          left: (selectionRect.left - canvasRect.left),
          top: (selectionRect.top - canvasRect.top),
          width: selectionRect.width,
          height: selectionRect.height
        };
      };
      
      // Setup resize handlers
      const handles = selection.querySelectorAll('.fie-crop-handle');
      handles.forEach(handle => {
        handle.addEventListener('mousedown', (e) => {
          e.stopPropagation();
          isResizing = true;
          currentHandle = handle.className.split('fie-crop-handle-')[1];
          
          startX = e.clientX;
          startY = e.clientY;
          startWidth = selection.offsetWidth;
          startHeight = selection.offsetHeight;
          startLeft = selection.offsetLeft;
          startTop = selection.offsetTop;
        });
      });
      
      // Setup drag handler for selection
      selection.addEventListener('mousedown', (e) => {
        if (!isResizing) { // Only start dragging if not resizing
          isDragging = true;
          startX = e.clientX;
          startY = e.clientY;
          startLeft = selection.offsetLeft;
          startTop = selection.offsetTop;
        }
      });
      
      // Setup mouse move handler
      overlay.addEventListener('mousemove', (e) => {
        if (!isDragging && !isResizing) return;
        
        e.preventDefault();
        
        const deltaX = e.clientX - startX;
        const deltaY = e.clientY - startY;
        
        if (isDragging) {
          // Update position
          let newLeft = startLeft + deltaX;
          let newTop = startTop + deltaY;
          
          // Constrain to canvas
          newLeft = Math.max(0, Math.min(newLeft, this.canvas.width - selection.offsetWidth));
          newTop = Math.max(0, Math.min(newTop, this.canvas.height - selection.offsetHeight));
          
          selection.style.left = `${newLeft}px`;
          selection.style.top = `${newTop}px`;
        } else if (isResizing) {
          // Handle resizing based on which handle was grabbed
          let newWidth = startWidth;
          let newHeight = startHeight;
          let newLeft = startLeft;
          let newTop = startTop;
          
          // Update dimensions and position based on handle
          switch (currentHandle) {
            case 'se':
              newWidth = startWidth + deltaX;
              newHeight = startHeight + deltaY;
              break;
            case 'sw':
              newWidth = startWidth - deltaX;
              newHeight = startHeight + deltaY;
              newLeft = startLeft + deltaX;
              break;
            case 'ne':
              newWidth = startWidth + deltaX;
              newHeight = startHeight - deltaY;
              newTop = startTop + deltaY;
              break;
            case 'nw':
              newWidth = startWidth - deltaX;
              newHeight = startHeight - deltaY;
              newLeft = startLeft + deltaX;
              newTop = startTop + deltaY;
              break;
            case 'n':
              newHeight = startHeight - deltaY;
              newTop = startTop + deltaY;
              break;
            case 's':
              newHeight = startHeight + deltaY;
              break;
            case 'e':
              newWidth = startWidth + deltaX;
              break;
            case 'w':
              newWidth = startWidth - deltaX;
              newLeft = startLeft + deltaX;
              break;
          }
          
          // Constrain to minimum size and canvas bounds
          newWidth = Math.max(20, newWidth);
          newHeight = Math.max(20, newHeight);
          
          // Constrain to canvas
          if (newLeft < 0) {
            newWidth += newLeft;
            newLeft = 0;
          }
          
          if (newTop < 0) {
            newHeight += newTop;
            newTop = 0;
          }
          
          if (newLeft + newWidth > this.canvas.width) {
            newWidth = this.canvas.width - newLeft;
          }
          
          if (newTop + newHeight > this.canvas.height) {
            newHeight = this.canvas.height - newTop;
          }
          
          // Apply new dimensions and position
          selection.style.width = `${newWidth}px`;
          selection.style.height = `${newHeight}px`;
          selection.style.left = `${newLeft}px`;
          selection.style.top = `${newTop}px`;
        }
      });
      
      // Setup mouse up handler
      const handleMouseUp = () => {
        isDragging = false;
        isResizing = false;
        currentHandle = null;
      };
      
      overlay.addEventListener('mouseup', handleMouseUp);
      overlay.addEventListener('mouseleave', handleMouseUp);
    }
    
    applyCrop(selection) {
      if (!this.canvas || !selection) return;
      
      // Get selection bounds
      const canvasRect = this.canvas.getBoundingClientRect();
      const selectionRect = selection.getBoundingClientRect();
      
      // Calculate crop dimensions
      const scale = this.canvas.width / canvasRect.width; // Account for any scaling
      
      const cropData = {
        x: (selectionRect.left - canvasRect.left) * scale,
        y: (selectionRect.top - canvasRect.top) * scale,
        width: selectionRect.width * scale,
        height: selectionRect.height * scale
      };
      
      // Save current state to history
      this.saveToHistory();
      
      // Apply crop
      this.crop(cropData);
    }
    
    exitCropMode() {
      // Remove crop overlay
      const overlay = this.container.querySelector('.fie-crop-overlay');
      if (overlay) {
        overlay.remove();
      }
    }
    
    // Watermark functionality
    applyWatermark(text, color, size) {
      if (!this.canvas || !this.imageLoaded || !text) return;
      
      // Save current state to history
      this.saveToHistory();
      
      const ctx = this.canvas.getContext('2d');
      
      // Set font properties
      ctx.font = `${size}px Arial`;
      ctx.fillStyle = color;
      ctx.globalAlpha = 0.5; // Semi-transparent
      
      // Position text in bottom right corner with padding
      const padding = 20;
      const textWidth = ctx.measureText(text).width;
      const x = this.canvas.width - textWidth - padding;
      const y = this.canvas.height - padding;
      
      // Draw watermark
      ctx.fillText(text, x, y);
      
      // Reset opacity
      ctx.globalAlpha = 1.0;
    }
    
    // Text annotation functionality
    addText(text, color, fontSize = 24, fontFamily = 'Arial', isBold = false, isItalic = false) {
      if (!this.canvas || !this.imageLoaded) return;
      
      // Check if text is empty
      if (!text || text.trim() === '') {
        console.log('Text is empty, not adding.');
        return;
      }
      
      console.log('Adding text:', text, 'with color:', color);
      
      const ctx = this.canvas.getContext('2d');
      
      // Build font style string
      let fontStyle = '';
      if (isBold) {
        fontStyle += 'bold ';
      }
      if (isItalic) {
        fontStyle += 'italic ';
      }
      
      // Set font properties with correct style
      ctx.font = `${fontStyle}${fontSize}px ${fontFamily}`;
      ctx.fillStyle = color || '#000000';
      ctx.textBaseline = 'middle';
      ctx.textAlign = 'center';
      
      // Position text in center
      const x = this.canvas.width / 2;
      const y = this.canvas.height / 2;
      
      // Add the text to the canvas
      ctx.fillText(text, x, y);
      
      // Save change to history
      this.pushHistory('Added text: ' + text);
      
      // Mark that the image has been modified
      this.imageModified = true;
    }
    
    // Update UI elements after actions
    updateUI() {
      console.log('Updating UI elements');
      
      // Show controls for current tab
      this.showControlsForTab(this.currentTab);
      
      // Update history buttons
      const undoButton = this.container.querySelector('.fie-undo-btn');
      if (undoButton) {
        undoButton.disabled = !this.history || this.history.length <= 1;
      }
      
      const redoButton = this.container.querySelector('.fie-redo-btn');
      if (redoButton) {
        redoButton.disabled = !this.redoStack || this.redoStack.length === 0;
      }
      
      // Update save button
      const saveButton = this.container.querySelector('.fie-save-btn');
      if (saveButton) {
        saveButton.disabled = !this.imageLoaded || !this.imageModified;
      }
      
      // Update reset button
      const resetButton = this.container.querySelector('.fie-reset-btn');
      if (resetButton) {
        resetButton.disabled = !this.imageLoaded || !this.imageModified;
      }
    }
  }
  
  // Add static properties
  FilerobotImageEditor.TABS = TABS;
  FilerobotImageEditor.TOOLS = TOOLS;
  
  // Export to window
  window.FilerobotImageEditor = FilerobotImageEditor;
  console.log('Filerobot Image Editor loaded with basic functionality');
})();
