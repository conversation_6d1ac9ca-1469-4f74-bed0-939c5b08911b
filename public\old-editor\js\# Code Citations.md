# Code Citations

## License: MIT
https://github.com/alexking/dry-land/blob/2071a2addd6884a30d2774d5021e881950d5f2c6/game/app/js/CanvasImage.js

```
);

            var imageData = ctx.getImageData(0, 0, canvas.width,
```


## License: unknown
https://github.com/aklevecz/skull-token/blob/3999210d70e904af7baabca0986d8c12474d051c/src/actions.js

```
);

            var imageData = ctx.getImageData(0, 0, canvas.width,
```


## License: MIT
https://github.com/alexking/dry-land/blob/2071a2addd6884a30d2774d5021e881950d5f2c6/game/app/js/CanvasImage.js

```
);

            var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            var data = imageData.data;

            for (var i = 0; i < data.length; i += 4) {
                var r = data[i];
                var g = data[i + 1];
                var b = data[i +
```


## License: unknown
https://github.com/aklevecz/skull-token/blob/3999210d70e904af7baabca0986d8c12474d051c/src/actions.js

```
);

            var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            var data = imageData.data;

            for (var i = 0; i < data.length; i += 4) {
                var r = data[i];
                var g = data[i + 1];
                var b = data[i +
```


## License: MIT
https://github.com/alexking/dry-land/blob/2071a2addd6884a30d2774d5021e881950d5f2c6/game/app/js/CanvasImage.js

```
);

            var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            var data = imageData.data;

            for (var i = 0; i < data.length; i += 4) {
                var r = data[i];
                var g = data[i + 1];
                var b = data[i + 2];
                var a = data[i + 3];
```


## License: unknown
https://github.com/aklevecz/skull-token/blob/3999210d70e904af7baabca0986d8c12474d051c/src/actions.js

```
);

            var imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            var data = imageData.data;

            for (var i = 0; i < data.length; i += 4) {
                var r = data[i];
                var g = data[i + 1];
                var b = data[i + 2];
                var a = data[i + 3];
```

