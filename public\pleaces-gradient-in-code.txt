\public\js\design-editor.js
  6,84:    console.log('🚀 NEW VERSION LOADED - design-editor.js v1751197090000 - CIRCULAR GRADIENT DESIGN-EDITOR FIX');
  820,12:            gradient: null, // For gradient colors
  820,35:            gradient: null, // For gradient colors
  1158,18:    // --- Simple Gradient Creation ---
  1159,25:    function createSimpleGradient(targetCtx, textObj, letterIndex = null, totalLetters = null) {
  1160,21:        if (!textObj.gradient || textObj.gradient.type === 'solid') {
  1160,41:        if (!textObj.gradient || textObj.gradient.type === 'solid') {
  1164,40:        console.log('🎨 Creating simple gradient for text:', textObj.text, 'letterIndex:', letterIndex, 'totalLetters:', totalLetters);
  1172,69:            // For single letter rendering, we need to calculate the gradient based on the full text
  1178,42:            console.log('🎨 Single letter gradient - Full text dimensions:', { textWidth, textHeight });
  1184,35:            console.log('🎨 Normal gradient - Text dimensions:', { textWidth, textHeight });
  1187,12:        let gradient;
  1189,20:        if (textObj.gradient.type === 'linear') {
  1190,34:            // Extract angle from gradient value (e.g., "linear-gradient(125deg, ...)")
  1190,64:            // Extract angle from gradient value (e.g., "linear-gradient(125deg, ...)")
  1192,24:            if (textObj.gradient.value) {
  1193,43:                const angleMatch = textObj.gradient.value.match(/linear-gradient\((\d+)deg/);
  1193,72:                const angleMatch = textObj.gradient.value.match(/linear-gradient\((\d+)deg/);
  1200,19:            // CSS gradients: 0deg = top to bottom, 90deg = left to right
  1201,22:            // Canvas gradients: 0deg = left to right, 90deg = top to bottom
  1209,48:                // For single letters, create a gradient that spans the letter width with correct angle
  1213,29:                // Calculate gradient line length based on letter dimensions and angle
  1217,29:                // Calculate gradient endpoints
  1223,16:                gradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
  1223,49:                gradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
  1224,61:                console.log('🎨 Created single letter linear gradient:', {
  1229,39:                console.log('🎨 LINEAR GRADIENT (NEW APPROACH): Creating gradient for full text using canvas-test method');
  1229,73:                console.log('🎨 LINEAR GRADIENT (NEW APPROACH): Creating gradient for full text using canvas-test method');
  1231,49:                // Use text metrics for accurate gradient sizing (same as canvas-test.html)
  1235,33:                // Create linear gradient across the text width (same as canvas-test.html)
  1236,16:                gradient = targetCtx.createLinearGradient(
  1236,49:                gradient = targetCtx.createLinearGradient(
  1241,39:                console.log('🎨 LINEAR GRADIENT (NEW APPROACH): Created gradient with text metrics:', {
  1241,72:                console.log('🎨 LINEAR GRADIENT (NEW APPROACH): Created gradient with text metrics:', {
  1248,55:                    note: 'Using simplified horizontal gradient like canvas-test.html'
  1252,27:        } else if (textObj.gradient.type === 'radial') {
  1254,35:            console.log('🎨 RADIAL GRADIENT (NEW APPROACH): Creating radial gradient using canvas-test method');
  1254,76:            console.log('🎨 RADIAL GRADIENT (NEW APPROACH): Creating radial gradient using canvas-test method');
  1256,45:            // Use text metrics for accurate gradient sizing (same as canvas-test.html)
  1264,12:            gradient = targetCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  1264,45:            gradient = targetCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  1265,35:            console.log('🎨 RADIAL GRADIENT (NEW APPROACH): Created radial gradient with text metrics:', {
  1265,75:            console.log('🎨 RADIAL GRADIENT (NEW APPROACH): Created radial gradient with text metrics:', {
  1275,12:        if (gradient && textObj.gradient.gradient.colors) {
  1275,32:        if (gradient && textObj.gradient.gradient.colors) {
  1275,41:        if (gradient && textObj.gradient.gradient.colors) {
  1277,70:                // For single letters, calculate which portion of the gradient this letter should show
  1294,39:                console.log('🎨 Letter gradient mapping:', {
  1299,87:                // Create color stops that represent only this letter's portion of the gradient
  1303,24:                textObj.gradient.gradient.colors.forEach(colorStop => {
  1303,33:                textObj.gradient.gradient.colors.forEach(colorStop => {
  1320,57:                    const interpolatedColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterCenterPercent);
  1320,79:                    const interpolatedColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterCenterPercent);
  1320,88:                    const interpolatedColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterCenterPercent);
  1332,54:                        const startColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterStartPercent);
  1332,76:                        const startColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterStartPercent);
  1332,85:                        const startColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterStartPercent);
  1339,52:                        const endColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterEndPercent);
  1339,74:                        const endColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterEndPercent);
  1339,83:                        const endColor = interpolateGradientColor(textObj.gradient.gradient.colors, letterEndPercent);
  1349,20:                    gradient.addColorStop(stop.position, stop.color);
  1354,26:                // Normal gradient for full text
  1355,24:                textObj.gradient.gradient.colors.forEach(colorStop => {
  1355,33:                textObj.gradient.gradient.colors.forEach(colorStop => {
  1356,20:                    gradient.addColorStop(colorStop.position / 100, colorStop.color);
  1362,15:        return gradient;
  1365,72:    // Helper function to interpolate color at a specific position in a gradient
  1366,24:    function interpolateGradientColor(colorStops, targetPercent) {
  1506,18:        // Handle gradient or solid color
  1507,54:        performanceLog('🎨 setTextContextOn - textObj.gradient:', textObj.gradient);
  1507,74:        performanceLog('🎨 setTextContextOn - textObj.gradient:', textObj.gradient);
  1511,54:        // Normal text and skew should use the normal gradient system
  1514,64:            // For letter-by-letter rendering, use solid color (gradient is handled by normal system)
  1518,53:            // For normal text, don't interfere with gradient system
  1519,83:            targetCtx.fillStyle = textObj.color; // Default, will be overridden by gradient system if needed
  1718,26:                // Update gradient picker if it exists
  1719,26:                const textGradientPicker = document.getElementById('textGradientPicker');
  1719,72:                const textGradientPicker = document.getElementById('textGradientPicker');
  1720,24:                if (textGradientPicker && textGradientPicker.gradientPickerInstance) {
  1720,46:                if (textGradientPicker && textGradientPicker.gradientPickerInstance) {
  1720,61:                if (textGradientPicker && textGradientPicker.gradientPickerInstance) {
  1721,39:                    if (selectedObject.gradient) {
  1722,28:                        textGradientPicker.gradientPickerInstance.setValue(selectedObject.gradient.value, selectedObject.gradient.type);
  1722,43:                        textGradientPicker.gradientPickerInstance.setValue(selectedObject.gradient.value, selectedObject.gradient.type);
  1722,90:                        textGradientPicker.gradientPickerInstance.setValue(selectedObject.gradient.value, selectedObject.gradient.type);
  1722,121:                        textGradientPicker.gradientPickerInstance.setValue(selectedObject.gradient.value, selectedObject.gradient.type);
  1724,28:                        textGradientPicker.gradientPickerInstance.setValue(selectedObject.color, 'solid');
  1724,43:                        textGradientPicker.gradientPickerInstance.setValue(selectedObject.color, 'solid');
  2535,111:            if (selectedObject.hasOwnProperty(property) || property.startsWith('gridDistort') || property === 'gradient' || property === 'colorIntensity') {
  2855,37:         } else if (property === 'svgGradient') {
  2856,31:             selectedObject.svgGradient = value;
  2857,22:             // Store gradient data directly on the object (like text gradients)
  2857,70:             // Store gradient data directly on the object (like text gradients)
  2859,32:                 selectedObject.gradient = value;
  2860,44:                 selectedObject.fillType = 'gradient';
  2861,37:                 console.log('🎨 SVG gradient stored:', value);
  2863,32:                 selectedObject.gradient = null;
  2865,37:                 console.log('🎨 SVG gradient cleared');
  2989,15:    // --- SVG Gradient Recoloring Function ---
  2990,33:    async function recolorSVGWithGradient(imageObject, gradientData) {
  2990,55:    async function recolorSVGWithGradient(imageObject, gradientData) {
  2992,37:            console.log('🎨 Applying gradient to SVG:', imageObject.imageUrl, gradientData);
  2992,78:            console.log('🎨 Applying gradient to SVG:', imageObject.imageUrl, gradientData);
  3003,49:            // Create a new SVG with the desired gradient
  3004,18:            const gradientSvgText = applySVGGradient(svgText, gradientData);
  3004,44:            const gradientSvgText = applySVGGradient(svgText, gradientData);
  3004,62:            const gradientSvgText = applySVGGradient(svgText, gradientData);
  3005,28:            console.log('🎨 Gradient SVG content preview:', gradientSvgText.substring(0, 200) + '...');
  3005,60:            console.log('🎨 Gradient SVG content preview:', gradientSvgText.substring(0, 200) + '...');
  3007,41:            // Create a blob URL for the gradient SVG
  3008,35:            const blob = new Blob([gradientSvgText], { type: 'image/svg+xml' });
  3009,18:            const gradientUrl = URL.createObjectURL(blob);
  3011,43:            // Create a new image with the gradient SVG
  3021,45:                imageObject.coloredBlobUrl = gradientUrl;
  3022,31:                imageObject.svgGradient = gradientData;
  3022,42:                imageObject.svgGradient = gradientData;
  3027,36:                console.log('🎨 SVG gradient applied successfully');
  3031,48:                console.error('🎨 Error loading gradient SVG:', imageObject.imageUrl, error);
  3032,34:                console.error('🎨 Gradient SVG content preview:', gradientSvgText.substring(0, 500) + '...');
  3032,66:                console.error('🎨 Gradient SVG content preview:', gradientSvgText.substring(0, 500) + '...');
  3033,36:                URL.revokeObjectURL(gradientUrl);
  3036,61:                console.log('🎨 Keeping original SVG without gradient change');
  3039,22:            img.src = gradientUrl;
  3042,45:            console.error('🎨 Error applying gradient to SVG:', error);
  3119,21:    function applySVGGradient(svgText, gradientData) {
  3119,39:    function applySVGGradient(svgText, gradientData) {
  3121,41:            console.log('🎨 Applying SVG gradient:', gradientData);
  3121,53:            console.log('🎨 Applying SVG gradient:', gradientData);
  3136,31:            // Create a unique gradient ID
  3137,18:            const gradientId = 'gradient_' + Math.random().toString(36).substring(2, 9);
  3137,32:            const gradientId = 'gradient_' + Math.random().toString(36).substring(2, 9);
  3146,22:            // Create gradient element
  3147,16:            let gradientElement;
  3148,16:            if (gradientData.type === 'radial') {
  3149,16:                gradientElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'radialGradient');
  3149,94:                gradientElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'radialGradient');
  3150,16:                gradientElement.setAttribute('cx', '50%');
  3151,16:                gradientElement.setAttribute('cy', '50%');
  3152,16:                gradientElement.setAttribute('r', '50%');
  3154,16:                gradientElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
  3154,94:                gradientElement = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
  3155,30:                const angle = gradientData.gradient.angle || 0;
  3155,43:                const angle = gradientData.gradient.angle || 0;
  3162,29:                // Calculate gradient endpoints (0-100% coordinates)
  3168,16:                gradientElement.setAttribute('x1', `${x1}%`);
  3169,16:                gradientElement.setAttribute('y1', `${y1}%`);
  3170,16:                gradientElement.setAttribute('x2', `${x2}%`);
  3171,16:                gradientElement.setAttribute('y2', `${y2}%`);
  3174,12:            gradientElement.setAttribute('id', gradientId);
  3174,47:            gradientElement.setAttribute('id', gradientId);
  3177,16:            if (gradientData.gradient.colors) {
  3177,29:            if (gradientData.gradient.colors) {
  3178,16:                gradientData.gradient.colors.forEach(colorStop => {
  3178,29:                gradientData.gradient.colors.forEach(colorStop => {
  3182,20:                    gradientElement.appendChild(stopElement);
  3186,19:            // Add gradient to defs
  3187,36:            defsElement.appendChild(gradientElement);
  3209,25:            // Apply the gradient to the root SVG element
  3210,52:            svgElement.setAttribute('fill', `url(#${gradientId})`);
  3221,32:            console.log('🎨 SVG gradient applied successfully');
  3225,46:            console.error('Error applying SVG gradient:', error);
  3227,34:            const fallbackColor = gradientData.gradient.colors[0].color;
  3227,47:            const fallbackColor = gradientData.gradient.colors[0].color;
  3232,11:    // SVG Gradient Fill Function (same as text gradients)
  3232,48:    // SVG Gradient Fill Function (same as text gradients)
  3233,21:    function applySVGGradientFill(ctx, obj, bounds) {
  3234,37:        console.log('🎨 APPLYING SVG GRADIENT FILL');
  3235,30:        console.log('%c🎨 SVG GRADIENT FILL: Starting gradient application', 'background-color: #335; color: #aff;');
  3235,54:        console.log('%c🎨 SVG GRADIENT FILL: Starting gradient application', 'background-color: #335; color: #aff;');
  3240,25:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3240,42:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3240,51:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3240,68:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3240,77:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3240,100:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3240,109:        if (!obj || !obj.gradient || !obj.gradient.gradient || !obj.gradient.gradient.colors || obj.gradient.gradient.colors.length < 2) {
  3241,36:            console.error('%c🎨 SVG GRADIENT FILL: Missing gradient data!', 'background-color: #500; color: #fee;', {
  3241,59:            console.error('%c🎨 SVG GRADIENT FILL: Missing gradient data!', 'background-color: #500; color: #fee;', {
  3243,16:                gradient: obj && obj.gradient ? JSON.stringify(obj.gradient) : 'No gradient'
  3243,37:                gradient: obj && obj.gradient ? JSON.stringify(obj.gradient) : 'No gradient'
  3243,67:                gradient: obj && obj.gradient ? JSON.stringify(obj.gradient) : 'No gradient'
  3243,83:                gradient: obj && obj.gradient ? JSON.stringify(obj.gradient) : 'No gradient'
  3251,51:        // Sort stops by position to ensure proper gradient rendering
  3252,36:        const sortedStops = [...obj.gradient.gradient.colors].sort((a, b) => a.position - b.position);
  3252,45:        const sortedStops = [...obj.gradient.gradient.colors].sort((a, b) => a.position - b.position);
  3254,30:        console.log('%c🎨 SVG GRADIENT FILL: Sorted stops', 'background-color: #335; color: #aff;',
  3257,34:        // Create the appropriate gradient
  3258,12:        let gradient;
  3261,20:            if (obj.gradient.type === 'radial') {
  3262,58:                // Calculate center and radius for radial gradient
  3267,38:                console.log('%c🎨 SVG GRADIENT FILL: Creating radial gradient', 'background-color: #335; color: #aff;', {
  3267,69:                console.log('%c🎨 SVG GRADIENT FILL: Creating radial gradient', 'background-color: #335; color: #aff;', {
  3273,16:                gradient = ctx.createRadialGradient(
  3273,43:                gradient = ctx.createRadialGradient(
  3278,37:                // Default to linear gradient
  3279,34:                const angle = obj.gradient.gradient.angle || 0;
  3279,43:                const angle = obj.gradient.gradient.angle || 0;
  3281,36:                // Convert angle to gradient coordinates
  3286,29:                // Calculate gradient endpoints
  3292,38:                console.log('%c🎨 SVG GRADIENT FILL: Creating linear gradient', 'background-color: #335; color: #aff;', {
  3292,69:                console.log('%c🎨 SVG GRADIENT FILL: Creating linear gradient', 'background-color: #335; color: #aff;', {
  3300,16:                gradient = ctx.createLinearGradient(startX, startY, endX, endY);
  3300,43:                gradient = ctx.createLinearGradient(startX, startY, endX, endY);
  3306,38:                console.log('%c🎨 SVG GRADIENT FILL: Adding color stop', 'background-color: #335; color: #aff;', {
  3310,16:                gradient.addColorStop(offset, stop.color);
  3313,23:            // Set the gradient as fill style
  3314,28:            ctx.fillStyle = gradient;
  3316,34:            console.log('%c🎨 SVG GRADIENT FILL: Gradient applied successfully!', 'background-color: #050; color: #afa;');
  3316,49:            console.log('%c🎨 SVG GRADIENT FILL: Gradient applied successfully!', 'background-color: #050; color: #afa;');
  3320,36:            console.error('%c🎨 SVG GRADIENT FILL: Error creating gradient', 'background-color: #500; color: #fee;', error);
  3320,66:            console.error('%c🎨 SVG GRADIENT FILL: Error creating gradient', 'background-color: #500; color: #fee;', error);
  3324,34:            console.log('%c🎨 SVG GRADIENT FILL: Using fallback color', 'background-color: #550; color: #ffa;', fallbackColor);
  3330,18:    // Initialize gradient color pickers
  3331,23:    function initializeGradientColorPickers() {
  3332,20:        // Wait for GradientColorPicker to be loaded
  3333,19:        if (typeof GradientColorPicker === 'undefined') {
  3334,25:            console.log('GradientColorPicker not loaded yet, retrying...');
  3335,33:            setTimeout(initializeGradientColorPickers, 200);
  3339,37:        console.log('🎨 Initializing gradient color pickers...');
  3344,22:            const textGradientPicker = document.createElement('div');
  3345,16:            textGradientPicker.id = 'textGradientPicker';
  3345,41:            textGradientPicker.id = 'textGradientPicker';
  3346,47:            textColorContainer.appendChild(textGradientPicker);
  3351,43:            const textPickerInstance = new GradientColorPicker(textGradientPicker, {
  3351,67:            const textPickerInstance = new GradientColorPicker(textGradientPicker, {
  3360,52:                        updateSelectedObjectFromUI('gradient', null); // Clear gradient when using solid color
  3360,79:                        updateSelectedObjectFromUI('gradient', null); // Clear gradient when using solid color
  3362,49:                        console.log('🎨 Applying gradient:', colorData);
  3365,34:                        // Handle gradient for text
  3366,52:                        updateSelectedObjectFromUI('gradient', colorData);
  3367,70:                        updateSelectedObjectFromUI('color', colorData.gradient.colors[0].color); // Set fallback color
  3373,16:            textGradientPicker.gradientPickerInstance = textPickerInstance;
  3373,31:            textGradientPicker.gradientPickerInstance = textPickerInstance;
  3379,21:            const svgGradientPicker = document.createElement('div');
  3380,15:            svgGradientPicker.id = 'svgGradientPicker';
  3380,39:            svgGradientPicker.id = 'svgGradientPicker';
  3381,45:            svgColorContainer.appendChild(svgGradientPicker);
  3386,16:            new GradientColorPicker(svgGradientPicker, {
  3386,39:            new GradientColorPicker(svgGradientPicker, {
  3392,55:                        updateSelectedObjectFromUI('svgGradient', null); // Clear gradient when using solid color
  3392,82:                        updateSelectedObjectFromUI('svgGradient', null); // Clear gradient when using solid color
  3394,53:                        console.log('🎨 Applying SVG gradient:', colorData);
  3395,55:                        updateSelectedObjectFromUI('svgGradient', colorData);
  3396,73:                        updateSelectedObjectFromUI('svgColor', colorData.gradient.colors[0].color); // Set fallback color
  4233,15:            hasGradient: textObj.gradient && textObj.gradient.type !== 'solid',
  4233,33:            hasGradient: textObj.gradient && textObj.gradient.type !== 'solid',
  4233,53:            hasGradient: textObj.gradient && textObj.gradient.type !== 'solid',
  4243,31:        // Skip if we're using gradient masking (front outline handled separately)
  4244,28:        const isCircularWithGradient = (textObj.distortType === 'circular' || textObj.effectMode === 'circle') && textObj.gradient && textObj.gradient.type !== 'solid';
  4244,122:        const isCircularWithGradient = (textObj.distortType === 'circular' || textObj.effectMode === 'circle') && textObj.gradient && textObj.gradient.type !== 'solid';
  4244,142:        const isCircularWithGradient = (textObj.distortType === 'circular' || textObj.effectMode === 'circle') && textObj.gradient && textObj.gradient.type !== 'solid';
  4245,31:        const isGridDistortWithGradient = (textObj.effectMode === 'gridDistort') && textObj.gradient && textObj.gradient.type !== 'solid';
  4245,92:        const isGridDistortWithGradient = (textObj.effectMode === 'gridDistort') && textObj.gradient && textObj.gradient.type !== 'solid';
  4245,112:        const isGridDistortWithGradient = (textObj.effectMode === 'gridDistort') && textObj.gradient && textObj.gradient.type !== 'solid';
  4246,26:        const isCurvedWithGradient = (textObj.effectMode === 'curve') && textObj.gradient && textObj.gradient.type !== 'solid';
  4246,81:        const isCurvedWithGradient = (textObj.effectMode === 'curve') && textObj.gradient && textObj.gradient.type !== 'solid';
  4246,101:        const isCurvedWithGradient = (textObj.effectMode === 'curve') && textObj.gradient && textObj.gradient.type !== 'solid';
  4247,28:        const isMeshWarpWithGradient = (textObj.effectMode === 'meshWarp') && textObj.gradient && textObj.gradient.type !== 'solid';
  4247,86:        const isMeshWarpWithGradient = (textObj.effectMode === 'meshWarp') && textObj.gradient && textObj.gradient.type !== 'solid';
  4247,106:        const isMeshWarpWithGradient = (textObj.effectMode === 'meshWarp') && textObj.gradient && textObj.gradient.type !== 'solid';
  4249,18:        const usesGradientMasking = isCircularWithGradient || isGridDistortWithGradient || isCurvedWithGradient || isMeshWarpWithGradient;
  4249,50:        const usesGradientMasking = isCircularWithGradient || isGridDistortWithGradient || isCurvedWithGradient || isMeshWarpWithGradient;
  4249,79:        const usesGradientMasking = isCircularWithGradient || isGridDistortWithGradient || isCurvedWithGradient || isMeshWarpWithGradient;
  4249,103:        const usesGradientMasking = isCircularWithGradient || isGridDistortWithGradient || isCurvedWithGradient || isMeshWarpWithGradient;
  4249,129:        const usesGradientMasking = isCircularWithGradient || isGridDistortWithGradient || isCurvedWithGradient || isMeshWarpWithGradient;
  4251,16:        if (usesGradientMasking) {
  4252,98:            console.log('🔍 FRONT OUTLINE #3: Skipping applyPerspectiveShadow_FrontOutline (using gradient masking)');
  4499,54:        // Get the current fill style (which may be a gradient set by setTextContextOn)
  4502,88:        // If opacity is less than 100%, apply it via globalAlpha instead of converting gradients to colors
  4504,39:            // Check if fillStyle is a gradient object (CanvasGradient)
  4504,62:            // Check if fillStyle is a gradient object (CanvasGradient)
  4505,112:            if (mainFillStyle && typeof mainFillStyle === 'object' && mainFillStyle.constructor.name === 'CanvasGradient') {
  4506,23:                // For gradients, use globalAlpha to preserve the gradient
  4506,66:                // For gradients, use globalAlpha to preserve the gradient
  4507,43:                console.log('🎨 Preserving gradient with opacity:', opacity);
  4638,322:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,355:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,704:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,737:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,784:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,860:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,910:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,951:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,1031:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,1082:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4638,1133:         else if (obj.decorationMode === "colorCut") { const distancePercent = obj.ccDist / 100; const baseColor = obj.ccColor; const colorOpacity = (obj.ccOpacity || 100) / 100; const textBaseColor = obj.color; const fillDirection = obj.ccFillDir; const gradStartY = topEdgeY; const gradEndY = topEdgeY + textHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } mainFillStyle = gradient; }
  4657,29:        // Apply opacity for gradients via globalAlpha
  4658,123:        if (opacity < 1 && mainFillStyle && typeof mainFillStyle === 'object' && mainFillStyle.constructor.name === 'CanvasGradient') {
  4660,52:            console.log('🎨 Applied globalAlpha for gradient:', opacity);
  4663,11:        // GRADIENT TEST: Create a simple test gradient to verify coordinates
  4663,47:        // GRADIENT TEST: Create a simple test gradient to verify coordinates
  4664,16:        if (obj.gradient && obj.gradient.type !== 'solid') {
  4664,32:        if (obj.gradient && obj.gradient.type !== 'solid') {
  4665,28:            console.log('🎨 GRADIENT TEST: Text position and size details');
  4666,28:            console.log('🎨 GRADIENT TEST: centerX:', centerX, 'centerY:', centerY);
  4667,28:            console.log('🎨 GRADIENT TEST: text:', text);
  4668,28:            console.log('🎨 GRADIENT TEST: obj.x:', obj.x, 'obj.y:', obj.y);
  4669,28:            console.log('🎨 GRADIENT TEST: obj.fontSize:', obj.fontSize);
  4673,28:            console.log('🎨 GRADIENT TEST: text metrics:', {
  4687,28:            console.log('🎨 GRADIENT TEST: calculated text bounds:', {
  4696,38:            // Create the actual user gradient based on type
  4697,18:            const gradientType = obj.gradient.type;
  4697,37:            const gradientType = obj.gradient.type;
  4698,28:            console.log('🎨 GRADIENT: Type:', gradientType);
  4698,46:            console.log('🎨 GRADIENT: Type:', gradientType);
  4700,41:            // Calculate text bounds for gradient positioning
  4706,20:            let userGradient;
  4708,16:            if (gradientType === 'radial') {
  4709,32:                console.log('🎨 GRADIENT: Creating radial gradient');
  4709,58:                console.log('🎨 GRADIENT: Creating radial gradient');
  4711,30:                // For radial gradients, create a circle that covers the text
  4715,32:                console.log('🎨 GRADIENT: Radial parameters:', {
  4723,33:                // Create radial gradient from center outward
  4724,20:                userGradient = targetCtx.createRadialGradient(
  4724,53:                userGradient = targetCtx.createRadialGradient(
  4730,32:                console.log('🎨 GRADIENT: Creating linear gradient');
  4730,58:                console.log('🎨 GRADIENT: Creating linear gradient');
  4732,26:                // Linear gradient with angle support
  4733,36:                const uiAngle = obj.gradient.gradient.angle || 0;
  4733,45:                const uiAngle = obj.gradient.gradient.angle || 0;
  4734,32:                console.log('🎨 GRADIENT: UI angle:', uiAngle);
  4741,32:                console.log('🎨 GRADIENT: angle calculations:', { angleRad, cos, sin });
  4743,29:                // Calculate gradient line length to ensure it covers the entire text
  4745,22:                const gradientLength = maxDimension * 1.5; // Extra length to ensure full coverage
  4747,29:                // Calculate gradient endpoints
  4748,42:                const x1 = centerTextX - (gradientLength / 2) * cos;
  4749,42:                const y1 = centerTextY - (gradientLength / 2) * sin;
  4750,42:                const x2 = centerTextX + (gradientLength / 2) * cos;
  4751,42:                const y2 = centerTextY + (gradientLength / 2) * sin;
  4753,32:                console.log('🎨 GRADIENT: final coordinates:', { x1, y1, x2, y2 });
  4755,20:                userGradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
  4755,53:                userGradient = targetCtx.createLinearGradient(x1, y1, x2, y2);
  4759,20:            if (obj.gradient.gradient.colors) {
  4759,29:            if (obj.gradient.gradient.colors) {
  4760,20:                obj.gradient.gradient.colors.forEach(colorStop => {
  4760,29:                obj.gradient.gradient.colors.forEach(colorStop => {
  4761,24:                    userGradient.addColorStop(colorStop.position / 100, colorStop.color);
  4762,36:                    console.log('🎨 GRADIENT: Added user color stop:', colorStop);
  4766,38:            targetCtx.fillStyle = userGradient;
  4767,28:            console.log('🎨 GRADIENT: Applied', gradientType, 'gradient');
  4767,48:            console.log('🎨 GRADIENT: Applied', gradientType, 'gradient');
  4767,63:            console.log('🎨 GRADIENT: Applied', gradientType, 'gradient');
  4855,54:        // Front outlines are now ONLY handled by the gradient masking system for ALL effects
  4856,94:        console.log('🔍 OLD FRONT OUTLINE: All old front outline systems removed - handled by gradient masking only');
  4893,63:        // Set font and measure text, passing letter index for gradient calculation
  4908,54:        // Get the current fill style (which may be a gradient set by setTextContextOn)
  4911,88:        // If opacity is less than 100%, apply it via globalAlpha instead of converting gradients to colors
  4913,39:            // Check if fillStyle is a gradient object (CanvasGradient)
  4913,62:            // Check if fillStyle is a gradient object (CanvasGradient)
  4914,118:            if (letterFillStyle && typeof letterFillStyle === 'object' && letterFillStyle.constructor.name === 'CanvasGradient') {
  4915,23:                // For gradients, use globalAlpha to preserve the gradient
  4915,66:                // For gradients, use globalAlpha to preserve the gradient
  4916,50:                console.log('🎨 Preserving letter gradient with opacity:', opacity);
  5036,1663: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,1696: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2045: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2078: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2125: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2201: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2251: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2292: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2372: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2423: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5036,2476: ⟪ 1462 characters skipped ⟫; const fillDirection = obj.ccFillDir; const expandedHeight = textHeight * coverage; const gradStartY = topEdgeY - (expandedHeight - textHeight) / 2; const gradEndY = gradStartY + expandedHeight; let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY); let color; if (baseColor.startsWith('#')) { const hex = baseColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${colorOpacity})`; } else { color = baseColor; } if (fillDirection === 'top') { gradient.addColorStop(0, color); gradient.addColorStop(distancePercent, color); gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor); gradient.addColorStop(1, textBaseColor); } else { gradient.addColorStop(0, textBaseColor); gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor); gradient.addColorStop(1 - distancePercent, color); gradient.addColorStop(1, color); } letterFillStyle = gradient; } else if (obj.decorationMode === "obliqueLines") { const weight = obj.oLineWeight; const distance = obj.oLineDist; const baseLineColor = obj.oLineColor; const lineOpacity = (obj.oOpacity || 100) / 100; const coverage = (obj.oCoverage || 100) / 100; const baseColor = obj.color; const size = weight + distance; const patternCanvas = document.createElement("canvas"); patternCanvas.width = size; patternCanvas.height = size; const pCtx = patternCanvas.getContext("2d"); pCtx.fillStyle = baseColor; pCtx.fillRect(0, 0, size, size); let color; if (baseLineColor.startsWith('#')) { const hex = baseLineColor.slice(1); const r = parseInt(hex.slice(0, 2), 16); const g = parseInt(hex.slice(2, 4), 16); const b = parseInt(hex.slice(4, 6), 16); color = `rgba(${r}, ${g}, ${b}, ${lineOpacity})`; } else { color = baseLineColor; } pCtx.strokeStyle = color; pCtx.lineWidth = weight; pCtx.beginPath(); for (let i = -size; i < size * 2; i += size) { pCtx.moveTo(i - distance, -distance); pCtx.lineTo(i +
  5050,29:        // Apply opacity for gradients via globalAlpha
  5051,129:        if (opacity < 1 && letterFillStyle && typeof letterFillStyle === 'object' && letterFillStyle.constructor.name === 'CanvasGradient') {
  5053,59:            console.log('🎨 Applied globalAlpha for letter gradient:', opacity);
  5109,48:        // Skip front outlines for effects with gradients that use gradient masking
  5109,67:        // Skip front outlines for effects with gradients that use gradient masking
  5110,28:        const isCircularWithGradient = (obj.distortType === 'circular' || obj.effectMode === 'circle') && obj.gradient && obj.gradient.type !== 'solid';
  5110,110:        const isCircularWithGradient = (obj.distortType === 'circular' || obj.effectMode === 'circle') && obj.gradient && obj.gradient.type !== 'solid';
  5110,126:        const isCircularWithGradient = (obj.distortType === 'circular' || obj.effectMode === 'circle') && obj.gradient && obj.gradient.type !== 'solid';
  5111,31:        const isGridDistortWithGradient = (obj.effectMode === 'gridDistort') && obj.gradient && obj.gradient.type !== 'solid';
  5111,84:        const isGridDistortWithGradient = (obj.effectMode === 'gridDistort') && obj.gradient && obj.gradient.type !== 'solid';
  5111,100:        const isGridDistortWithGradient = (obj.effectMode === 'gridDistort') && obj.gradient && obj.gradient.type !== 'solid';
  5112,26:        const isCurvedWithGradient = (obj.effectMode === 'curve') && obj.gradient && obj.gradient.type !== 'solid';
  5112,73:        const isCurvedWithGradient = (obj.effectMode === 'curve') && obj.gradient && obj.gradient.type !== 'solid';
  5112,89:        const isCurvedWithGradient = (obj.effectMode === 'curve') && obj.gradient && obj.gradient.type !== 'solid';
  5113,28:        const isMeshWarpWithGradient = (obj.effectMode === 'meshWarp') && obj.gradient && obj.gradient.type !== 'solid';
  5113,78:        const isMeshWarpWithGradient = (obj.effectMode === 'meshWarp') && obj.gradient && obj.gradient.type !== 'solid';
  5113,94:        const isMeshWarpWithGradient = (obj.effectMode === 'meshWarp') && obj.gradient && obj.gradient.type !== 'solid';
  5116,54:        // Front outlines are now ONLY handled by the gradient masking system
  5117,112:        console.log('🔍 OLD FRONT OUTLINE: All front outline systems removed from letter rendering - handled by gradient masking only');
  5132,28:        console.log(`🔍 NEW GRADIENT SYSTEM: Bypassing old gradient check - using canvas-test.html approach`);
  5132,59:        console.log(`🔍 NEW GRADIENT SYSTEM: Bypassing old gradient check - using canvas-test.html approach`);
  5133,28:        console.log(`🔍 NEW GRADIENT SYSTEM: obj.gradient:`, obj.gradient);
  5133,49:        console.log(`🔍 NEW GRADIENT SYSTEM: obj.gradient:`, obj.gradient);
  5133,65:        console.log(`🔍 NEW GRADIENT SYSTEM: obj.gradient:`, obj.gradient);
  5134,28:        console.log(`🔍 NEW GRADIENT SYSTEM: Will render using new high-resolution method regardless of gradient state`);
  5134,104:        console.log(`🔍 NEW GRADIENT SYSTEM: Will render using new high-resolution method regardless of gradient state`);
  5136,34:        // Check if we should use gradient rendering
  5137,23:        const shouldUseGradient = obj.gradient && obj.gradient.type !== 'solid';
  5137,38:        const shouldUseGradient = obj.gradient && obj.gradient.type !== 'solid';
  5137,54:        const shouldUseGradient = obj.gradient && obj.gradient.type !== 'solid';
  5138,28:        console.log(`🔍 NEW GRADIENT SYSTEM: Should use gradient:`, shouldUseGradient);
  5138,56:        console.log(`🔍 NEW GRADIENT SYSTEM: Should use gradient:`, shouldUseGradient);
  5138,77:        console.log(`🔍 NEW GRADIENT SYSTEM: Should use gradient:`, shouldUseGradient);
  5139,28:        console.log(`🔍 NEW GRADIENT SYSTEM: obj.gradient:`, obj.gradient);
  5139,49:        console.log(`🔍 NEW GRADIENT SYSTEM: obj.gradient:`, obj.gradient);
  5139,65:        console.log(`🔍 NEW GRADIENT SYSTEM: obj.gradient:`, obj.gradient);
  5141,23:        // REMOVED OLD GRADIENT CHECK - Always use the new approach
  5229,84:                // Create a temporary object for this letter with original text for gradient calculation
  5288,59:        // Draw front outlines on top for normal text (non-gradient)
  5291,111:            console.log('🔍 RENDER ORDER: Drawing perspective shadow front outline on top for normal text (non-gradient)');
  5308,104:            console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top for normal text (non-gradient)');
  5383,11:    // New gradient masking function for circular text
  5384,35:    function drawCircularObjectWithGradientMask(obj, targetCtx) {
  5385,24:        console.log('🎨 GRADIENT MASK: Drawing circular text with gradient mask');
  5385,66:        console.log('🎨 GRADIENT MASK: Drawing circular text with gradient mask');
  5387,25:        // Save original gradient
  5388,22:        const originalGradient = obj.gradient;
  5388,37:        const originalGradient = obj.gradient;
  5396,24:        console.log('🔍 GRADIENT DEBUG: Canvas setup');
  5418,12:        obj.gradient = { type: 'solid' };
  5434,24:        console.log('🔍 GRADIENT DEBUG: Text canvas setup');
  5495,24:        console.log('🔍 GRADIENT FLOW: Step 3 - Cutting out text from effects canvas using destination-out');
  5499,24:        console.log('🔍 GRADIENT FLOW: Step 3 complete - Text cutout applied, composite operation reset');
  5501,26:        // Step 4: Create gradient canvas with high-DPI scaling
  5502,14:        const gradientCanvas = document.createElement('canvas');
  5503,8:        gradientCanvas.width = logicalWidth * scaleFactor;
  5504,8:        gradientCanvas.height = logicalHeight * scaleFactor;
  5505,24:        console.log('🔍 GRADIENT DEBUG: Gradient canvas setup');
  5505,40:        console.log('🔍 GRADIENT DEBUG: Gradient canvas setup');
  5506,25:        console.log('  - gradientCanvas.width:', gradientCanvas.width);
  5506,49:        console.log('  - gradientCanvas.width:', gradientCanvas.width);
  5507,25:        console.log('  - gradientCanvas.height:', gradientCanvas.height);
  5507,50:        console.log('  - gradientCanvas.height:', gradientCanvas.height);
  5509,14:        const gradientCtx = gradientCanvas.getContext('2d');
  5509,28:        const gradientCtx = gradientCanvas.getContext('2d');
  5510,8:        gradientCtx.scale(scaleFactor, scaleFactor);
  5511,25:        console.log('  - gradientCtx scale applied:', scaleFactor);
  5512,8:        gradientCtx.imageSmoothingEnabled = true;
  5513,8:        gradientCtx.imageSmoothingQuality = 'high';
  5515,22:        // Create the gradient
  5516,14:        const gradientType = originalGradient.type;
  5516,37:        const gradientType = originalGradient.type;
  5517,12:        let gradient;
  5519,12:        if (gradientType === 'radial') {
  5523,28:            console.log('🔍 GRADIENT DEBUG: Radial gradient');
  5523,51:            console.log('🔍 GRADIENT DEBUG: Radial gradient');
  5525,12:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  5525,23:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  5525,47:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  5527,22:            // Linear gradient
  5528,35:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  5528,44:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  5540,12:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  5540,23:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  5540,47:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  5544,20:        if (originalGradient.gradient.colors) {
  5544,29:        if (originalGradient.gradient.colors) {
  5545,20:            originalGradient.gradient.colors.forEach(colorStop => {
  5545,29:            originalGradient.gradient.colors.forEach(colorStop => {
  5546,16:                gradient.addColorStop(colorStop.position / 100, colorStop.color);
  5550,21:        // Fill with gradient
  5551,8:        gradientCtx.fillStyle = gradient;
  5551,32:        gradientCtx.fillStyle = gradient;
  5552,24:        console.log('🔍 GRADIENT DEBUG: Filling gradient');
  5552,48:        console.log('🔍 GRADIENT DEBUG: Filling gradient');
  5554,8:        gradientCtx.fillRect(0, 0, logicalWidth, logicalHeight);
  5556,33:        // Apply text as mask to gradient
  5557,8:        gradientCtx.globalCompositeOperation = 'destination-in';
  5558,24:        console.log('🔍 GRADIENT DEBUG: Applying text mask');
  5561,8:        gradientCtx.drawImage(textCanvas, 0, 0);
  5565,24:        console.log('🔍 GRADIENT DEBUG: Drawing tempCanvas to target');
  5571,24:        // Step 6: Draw gradient text on top
  5572,55:        console.log('🔍 RENDER ORDER: Step 6 - Drawing gradient text');
  5573,24:        console.log('🔍 GRADIENT DEBUG: Drawing gradientCanvas to target');
  5573,48:        console.log('🔍 GRADIENT DEBUG: Drawing gradientCanvas to target');
  5574,25:        console.log('  - gradientCanvas size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5574,48:        console.log('  - gradientCanvas size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5574,75:        console.log('  - gradientCanvas size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5576,28:        targetCtx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height, -logicalWidth / 2, -logicalHeight / 2, logicalWidth, logicalHeight);
  5576,50:        targetCtx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height, -logicalWidth / 2, -logicalHeight / 2, logicalWidth, logicalHeight);
  5576,72:        targetCtx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height, -logicalWidth / 2, -logicalHeight / 2, logicalWidth, logicalHeight);
  5578,54:        // Step 7: Apply decoration effects on top of gradient text
  5579,85:        console.log('🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient');
  5636,86:            console.log('🎨 DECORATION: Applied decoration effects on top of circular gradient text');
  5660,28:        // Restore original gradient
  5661,12:        obj.gradient = originalGradient;
  5661,31:        obj.gradient = originalGradient;
  5663,24:        console.log('🎨 GRADIENT MASK: Circular text with gradient mask complete');
  5663,58:        console.log('🎨 GRADIENT MASK: Circular text with gradient mask complete');
  5666,11:    // New gradient masking function for normal text
  5667,33:    function drawNormalObjectWithGradientMask(obj, targetCtx) {
  5668,24:        console.log('🎨 GRADIENT MASK: Drawing normal text with gradient mask');
  5668,64:        console.log('🎨 GRADIENT MASK: Drawing normal text with gradient mask');
  5669,24:        console.log('🔍 GRADIENT FLOW: Starting normal text gradient rendering');
  5669,60:        console.log('🔍 GRADIENT FLOW: Starting normal text gradient rendering');
  5670,24:        console.log('🔍 GRADIENT FLOW: Target canvas info:');
  5678,25:        // Save original gradient
  5679,22:        const originalGradient = obj.gradient;
  5679,37:        const originalGradient = obj.gradient;
  5696,24:        console.log('🔍 GRADIENT FLOW: Step 1 - Drawing effects and text');
  5702,12:        obj.gradient = { type: 'solid' };
  5746,56:            console.error("Error drawing normal text in gradient mask:", e);
  5760,24:        console.log('🔍 GRADIENT FLOW: Step 2 - Creating text mask');
  5767,24:        console.log('🔍 GRADIENT FLOW: Drawing text mask at logical center:', logicalWidth / 2, logicalHeight / 2);
  5772,46:        // Handle letter spacing properly for gradient mask
  5802,24:        console.log('🔍 GRADIENT FLOW: Step 3 - Cutting out text from effects canvas using destination-out');
  5806,24:        console.log('🔍 GRADIENT FLOW: Step 3 complete - Text cutout applied, composite operation reset');
  5808,26:        // Step 4: Create gradient canvas with high-DPI scaling
  5809,14:        const gradientCanvas = document.createElement('canvas');
  5810,8:        gradientCanvas.width = logicalWidth * scaleFactor;
  5811,8:        gradientCanvas.height = logicalHeight * scaleFactor;
  5812,14:        const gradientCtx = gradientCanvas.getContext('2d');
  5812,28:        const gradientCtx = gradientCanvas.getContext('2d');
  5813,8:        gradientCtx.scale(scaleFactor, scaleFactor);
  5814,24:        console.log('🔍 GRADIENT FLOW: Step 4 - Creating gradient canvas');
  5814,57:        console.log('🔍 GRADIENT FLOW: Step 4 - Creating gradient canvas');
  5815,25:        console.log('  - gradientCanvas size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5815,48:        console.log('  - gradientCanvas size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5815,75:        console.log('  - gradientCanvas size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5816,25:        console.log('  - gradientCtx scale applied:', scaleFactor);
  5817,8:        gradientCtx.imageSmoothingEnabled = true;
  5818,8:        gradientCtx.imageSmoothingQuality = 'high';
  5820,22:        // Create the gradient
  5821,14:        const gradientType = originalGradient.type;
  5821,37:        const gradientType = originalGradient.type;
  5822,12:        let gradient;
  5824,12:        if (gradientType === 'radial') {
  5828,28:            console.log('🔍 GRADIENT FLOW: Creating radial gradient at logical coords:', centerX, centerY, 'radius:', radius);
  5828,59:            console.log('🔍 GRADIENT FLOW: Creating radial gradient at logical coords:', centerX, centerY, 'radius:', radius);
  5829,12:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  5829,23:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  5829,47:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  5831,22:            // Linear gradient
  5832,35:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  5832,44:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  5838,28:            console.log('🔍 GRADIENT FLOW: Creating linear gradient at logical coords:', centerX, centerY, 'length:', length);
  5838,59:            console.log('🔍 GRADIENT FLOW: Creating linear gradient at logical coords:', centerX, centerY, 'length:', length);
  5845,12:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  5845,23:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  5845,47:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  5849,20:        if (originalGradient.gradient.colors) {
  5849,29:        if (originalGradient.gradient.colors) {
  5850,20:            originalGradient.gradient.colors.forEach(colorStop => {
  5850,29:            originalGradient.gradient.colors.forEach(colorStop => {
  5851,16:                gradient.addColorStop(colorStop.position / 100, colorStop.color);
  5855,21:        // Fill with gradient
  5856,8:        gradientCtx.fillStyle = gradient;
  5856,32:        gradientCtx.fillStyle = gradient;
  5857,24:        console.log('🔍 GRADIENT FLOW: Filling gradient at logical size:', logicalWidth, 'x', logicalHeight);
  5857,47:        console.log('🔍 GRADIENT FLOW: Filling gradient at logical size:', logicalWidth, 'x', logicalHeight);
  5858,8:        gradientCtx.fillRect(0, 0, logicalWidth, logicalHeight);
  5860,33:        // Apply text as mask to gradient
  5861,8:        gradientCtx.globalCompositeOperation = 'destination-in';
  5862,8:        gradientCtx.drawImage(textCanvas, 0, 0);
  5866,24:        console.log('🔍 GRADIENT FLOW: Drawing tempCanvas - source size:', tempCanvas.width, 'x', tempCanvas.height);
  5867,24:        console.log('🔍 GRADIENT FLOW: Drawing tempCanvas at position 0,0 with size:', logicalWidth, 'x', logicalHeight);
  5870,24:        // Step 6: Draw gradient text on top
  5871,55:        console.log('🔍 RENDER ORDER: Step 6 - Drawing gradient text for normal text');
  5872,24:        console.log('🔍 GRADIENT FLOW: Drawing gradientCanvas - source size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5872,47:        console.log('🔍 GRADIENT FLOW: Drawing gradientCanvas - source size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5872,79:        console.log('🔍 GRADIENT FLOW: Drawing gradientCanvas - source size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5872,106:        console.log('🔍 GRADIENT FLOW: Drawing gradientCanvas - source size:', gradientCanvas.width, 'x', gradientCanvas.height);
  5873,24:        console.log('🔍 GRADIENT FLOW: Drawing gradientCanvas at position 0,0 with size:', logicalWidth, 'x', logicalHeight);
  5873,47:        console.log('🔍 GRADIENT FLOW: Drawing gradientCanvas at position 0,0 with size:', logicalWidth, 'x', logicalHeight);
  5874,28:        targetCtx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height, -logicalWidth / 2, -logicalHeight / 2, logicalWidth, logicalHeight);
  5874,50:        targetCtx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height, -logicalWidth / 2, -logicalHeight / 2, logicalWidth, logicalHeight);
  5874,72:        targetCtx.drawImage(gradientCanvas, 0, 0, gradientCanvas.width, gradientCanvas.height, -logicalWidth / 2, -logicalHeight / 2, logicalWidth, logicalHeight);
  5876,54:        // Step 7: Apply decoration effects on top of gradient text
  5877,85:        console.log('🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for normal text');
  5968,19:            // For gradient text decorations, we need to apply them more prominently
  5969,92:            // First, apply the decoration with higher opacity to make it more visible over gradients
  5972,60:            // Apply decoration with multiply to darken the gradient where decoration exists
  5983,88:            console.log('🎨 DECORATION: Applied decoration with dual-layer blending for gradient text');
  5985,84:            console.log('🎨 DECORATION: Applied decoration effects on top of normal gradient text');
  6009,28:        // Restore original gradient
  6010,12:        obj.gradient = originalGradient;
  6010,31:        obj.gradient = originalGradient;
  6012,24:        console.log('🎨 GRADIENT MASK: Normal text with gradient mask complete');
  6012,56:        console.log('🎨 GRADIENT MASK: Normal text with gradient mask complete');
  6015,49:    // Normal front outline drawing function for gradient masking
  6059,24:        console.log('🎨 GRADIENT MASK: Drew normal front outline');
  6102,81:    // Normal front outline drawing function with letter spacing support for non-gradient text
  6141,54:    // Standalone front outline drawing functions for gradient masking
  6178,24:        console.log('🎨 GRADIENT MASK: Drew perspective front outline on top');
  6214,24:        console.log('🎨 GRADIENT MASK: Drew detailed 3D front outline on top');
  6287,24:        console.log('🎨 GRADIENT MASK: Drew circular front outline');
  6532,29:            // Save original gradient and set to solid for mask
  6533,26:            const originalGradient = obj.gradient;
  6533,41:            const originalGradient = obj.gradient;
  6534,16:            obj.gradient = { type: 'solid' };
  6539,23:            // Restore gradient
  6540,16:            obj.gradient = originalGradient;
  6540,35:            obj.gradient = originalGradient;
  6587,89:        console.log('🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline called from gradient masking system');
  6596,15:            hasGradient: obj.gradient && obj.gradient.type !== 'solid'
  6596,29:            hasGradient: obj.gradient && obj.gradient.type !== 'solid'
  6596,45:            hasGradient: obj.gradient && obj.gradient.type !== 'solid'
  6778,88:        console.log('🔍 FRONT OUTLINE SOURCE #1: drawGridDistortFrontOutline COMPLETED (gradient masking system)');
  6783,24:        console.log('🎨 GRADIENT MASK: Drawing Curved text front outline');
  6839,24:        console.log('🎨 GRADIENT MASK: Drew Curved text front outline');
  6963,15:            hasGradient: !!(obj.gradient && obj.gradient.type !== 'solid'),
  6963,32:            hasGradient: !!(obj.gradient && obj.gradient.type !== 'solid'),
  6963,48:            hasGradient: !!(obj.gradient && obj.gradient.type !== 'solid'),
  6964,12:            gradientType: obj.gradient?.type,
  6964,30:            gradientType: obj.gradient?.type,
  6972,37:        // Check if we need to apply gradient masking
  6973,16:        if (obj.gradient && obj.gradient.type !== 'solid') {
  6973,32:        if (obj.gradient && obj.gradient.type !== 'solid') {
  6974,49:            console.log('🔄 CIRCULAR MAIN: Using gradient masking path');
  6975,34:            drawCircularObjectWithGradientMask(obj, targetCtx);
  7088,27:            // 🔍 CIRCULAR GRADIENT FIX: Check for gradient and apply it per letter
  7088,51:            // 🔍 CIRCULAR GRADIENT FIX: Check for gradient and apply it per letter
  7089,37:            console.log(`🔄 CIRCULAR GRADIENT ${i}: Checking for gradient on letter "${letter}"`);
  7089,65:            console.log(`🔄 CIRCULAR GRADIENT ${i}: Checking for gradient on letter "${letter}"`);
  7090,27:            const shouldUseGradient = obj.gradient && obj.gradient.type !== 'solid';
  7090,42:            const shouldUseGradient = obj.gradient && obj.gradient.type !== 'solid';
  7090,58:            const shouldUseGradient = obj.gradient && obj.gradient.type !== 'solid';
  7091,37:            console.log(`🔄 CIRCULAR GRADIENT ${i}: Should use gradient:`, shouldUseGradient);
  7091,63:            console.log(`🔄 CIRCULAR GRADIENT ${i}: Should use gradient:`, shouldUseGradient);
  7091,84:            console.log(`🔄 CIRCULAR GRADIENT ${i}: Should use gradient:`, shouldUseGradient);
  7092,37:            console.log(`🔄 CIRCULAR GRADIENT ${i}: obj.gradient:`, obj.gradient);
  7092,56:            console.log(`🔄 CIRCULAR GRADIENT ${i}: obj.gradient:`, obj.gradient);
  7092,72:            console.log(`🔄 CIRCULAR GRADIENT ${i}: obj.gradient:`, obj.gradient);
  7094,25:            if (shouldUseGradient) {
  7095,41:                console.log(`🔄 CIRCULAR GRADIENT ${i}: Applying gradient to letter "${letter}"`);
  7095,65:                console.log(`🔄 CIRCULAR GRADIENT ${i}: Applying gradient to letter "${letter}"`);
  7097,24:                if (obj.gradient.type === 'linear') {
  7098,38:                    const angle = obj.gradient.gradient.angle || 0;
  7098,47:                    const angle = obj.gradient.gradient.angle || 0;
  7101,46:                    // 🔧 HIGH-DPI FIX: Scale gradient coordinates for high-resolution canvas
  7104,45:                    console.log(`🔄 CIRCULAR GRADIENT SCALE ${i}: Current scale factor: ${scaleFactor}`);
  7106,30:                    // Create gradient for this letter's coordinate system with proper scaling
  7107,26:                    const gradientLength = diameter * scaleFactor;
  7108,45:                    console.log(`🔄 CIRCULAR GRADIENT SCALE ${i}: Original diameter=${diameter}, scaled gradientLength=${gradientLength}`);
  7108,104:                    console.log(`🔄 CIRCULAR GRADIENT SCALE ${i}: Original diameter=${diameter}, scaled gradientLength=${gradientLength}`);
  7108,121:                    console.log(`🔄 CIRCULAR GRADIENT SCALE ${i}: Original diameter=${diameter}, scaled gradientLength=${gradientLength}`);
  7110,36:                    const startX = -gradientLength / 2 * Math.cos(angleRad);
  7111,36:                    const startY = -gradientLength / 2 * Math.sin(angleRad);
  7112,33:                    const endX = gradientLength / 2 * Math.cos(angleRad);
  7113,33:                    const endY = gradientLength / 2 * Math.sin(angleRad);
  7115,45:                    console.log(`🔄 CIRCULAR GRADIENT COORDS ${i}: startX=${startX.toFixed(2)}, startY=${startY.toFixed(2)}, endX=${endX.toFixed(2)}, endY=${endY.toFixed(2)}`);
  7117,26:                    const gradient = targetCtx.createLinearGradient(startX, startY, endX, endY);
  7117,59:                    const gradient = targetCtx.createLinearGradient(startX, startY, endX, endY);
  7120,24:                    obj.gradient.gradient.colors.forEach(colorStop => {
  7120,33:                    obj.gradient.gradient.colors.forEach(colorStop => {
  7122,24:                        gradient.addColorStop(position, colorStop.color);
  7125,38:                    letterFillStyle = gradient;
  7126,45:                    console.log(`🔄 CIRCULAR GRADIENT ${i}: Applied linear gradient to letter "${letter}"`);
  7126,75:                    console.log(`🔄 CIRCULAR GRADIENT ${i}: Applied linear gradient to letter "${letter}"`);
  7128,31:                } else if (obj.gradient.type === 'radial') {
  7129,53:                    // 🔧 HIGH-DPI FIX: Scale radial gradient radius for high-resolution canvas
  7133,45:                    console.log(`🔄 CIRCULAR GRADIENT SCALE ${i}: Original radius=${radius}, scaled radius=${scaledRadius}`);
  7135,26:                    const gradient = targetCtx.createRadialGradient(0, 0, 0, 0, 0, scaledRadius);
  7135,59:                    const gradient = targetCtx.createRadialGradient(0, 0, 0, 0, 0, scaledRadius);
  7138,24:                    obj.gradient.gradient.colors.forEach(colorStop => {
  7138,33:                    obj.gradient.gradient.colors.forEach(colorStop => {
  7140,24:                        gradient.addColorStop(position, colorStop.color);
  7143,38:                    letterFillStyle = gradient;
  7144,45:                    console.log(`🔄 CIRCULAR GRADIENT ${i}: Applied radial gradient to letter "${letter}" with scaled radius=${scaledRadius}`);
  7144,75:                    console.log(`🔄 CIRCULAR GRADIENT ${i}: Applied radial gradient to letter "${letter}" with scaled radius=${scaledRadius}`);
  7147,41:                console.log(`🔄 CIRCULAR GRADIENT ${i}: Using solid color for letter "${letter}":`, obj.color);
  7189,37:                // Create a vertical gradient for the letter
  7194,20:                let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY);
  7194,53:                let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY);
  7225,20:                    gradient.addColorStop(0, blendedColor);
  7226,20:                    gradient.addColorStop(distancePercent, blendedColor);
  7227,20:                    gradient.addColorStop(Math.min(1, distancePercent + 0.001), textBaseColor);
  7228,20:                    gradient.addColorStop(1, textBaseColor);
  7230,20:                    gradient.addColorStop(0, textBaseColor);
  7231,20:                    gradient.addColorStop(Math.max(0, 1 - distancePercent - 0.001), textBaseColor);
  7232,20:                    gradient.addColorStop(1 - distancePercent, blendedColor);
  7233,20:                    gradient.addColorStop(1, blendedColor);
  7235,34:                letterFillStyle = gradient;
  7316,28:                // Create a gradient with solid block + fading lines
  7321,20:                let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY);
  7321,53:                let gradient = targetCtx.createLinearGradient(0, gradStartY, 0, gradEndY);
  7339,20:                    gradient.addColorStop(0, baseColor);
  7340,20:                    gradient.addColorStop(Math.max(0, startPos - 0.001), baseColor);
  7343,20:                    gradient.addColorStop(startPos, solidBlendedColor);
  7344,20:                    gradient.addColorStop(solidBlockStart, solidBlendedColor);
  7360,28:                            gradient.addColorStop(linePos, baseColor);
  7361,28:                            gradient.addColorStop(Math.min(1, linePos + lineWidth), blendedColor);
  7363,32:                                gradient.addColorStop(Math.min(1, linePos + lineWidth + lineSpacing), baseColor);
  7367,20:                    gradient.addColorStop(1, baseColor);
  7370,20:                    gradient.addColorStop(0, solidBlendedColor);
  7371,20:                    gradient.addColorStop(solidBlockPercent, solidBlendedColor);
  7372,20:                    gradient.addColorStop(Math.min(1, solidBlockPercent + 0.001), baseColor);
  7388,28:                            gradient.addColorStop(linePos, baseColor);
  7389,28:                            gradient.addColorStop(Math.min(distancePercent, linePos + lineWidth), blendedColor);
  7391,32:                                gradient.addColorStop(Math.min(distancePercent, linePos + lineWidth + lineSpacing), baseColor);
  7395,20:                    gradient.addColorStop(distancePercent, baseColor);
  7396,20:                    gradient.addColorStop(1, baseColor);
  7399,34:                letterFillStyle = gradient;
  7451,56:        // Add front outlines for circular text without gradients
  7452,99:        console.log('🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Circular text (no gradient)');
  7454,112:            console.log('🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Circular text (no gradient)');
  7463,105:            console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top for Circular text (no gradient)');
  7482,37:        // Check if we need to apply gradient masking
  7483,16:        if (obj.gradient && obj.gradient.type !== 'solid') {
  7483,32:        if (obj.gradient && obj.gradient.type !== 'solid') {
  7484,32:            drawCurvedObjectWithGradientMask(obj, targetCtx);
  7506,59:            // Create letter object with original text for gradient calculation
  7553,54:        // Add front outlines for curved text without gradients
  7554,97:        console.log('🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Curved text (no gradient)');
  7556,110:            console.log('🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Curved text (no gradient)');
  7565,103:            console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top for Curved text (no gradient)');
  7575,7:    // Gradient masking function for curved text
  7576,33:    function drawCurvedObjectWithGradientMask(obj, targetCtx) {
  7577,24:        console.log('🎨 GRADIENT MASK: Drawing curved text with gradient mask');
  7577,64:        console.log('🎨 GRADIENT MASK: Drawing curved text with gradient mask');
  7579,25:        // Save original gradient
  7580,22:        const originalGradient = obj.gradient;
  7580,37:        const originalGradient = obj.gradient;
  7595,12:        obj.gradient = { type: 'solid' };
  7687,45:        // Create a temporary object without gradient and effects for shape rendering
  7690,12:            gradient: { type: 'solid' },
  7771,26:        // Step 4: Create gradient canvas with high-DPI scaling
  7772,14:        const gradientCanvas = document.createElement('canvas');
  7773,8:        gradientCanvas.width = 2000 * scaleFactor;
  7774,8:        gradientCanvas.height = 2000 * scaleFactor;
  7775,14:        const gradientCtx = gradientCanvas.getContext('2d');
  7775,28:        const gradientCtx = gradientCanvas.getContext('2d');
  7776,8:        gradientCtx.scale(scaleFactor, scaleFactor);
  7777,8:        gradientCtx.imageSmoothingEnabled = true;
  7778,8:        gradientCtx.imageSmoothingQuality = 'high';
  7780,22:        // Create the gradient
  7781,14:        const gradientType = originalGradient.type;
  7781,37:        const gradientType = originalGradient.type;
  7782,12:        let gradient;
  7784,12:        if (gradientType === 'radial') {
  7785,28:            const centerX = gradientCanvas.width / 2;
  7786,28:            const centerY = gradientCanvas.height / 2;
  7787,36:            const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
  7787,58:            const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
  7788,12:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  7788,23:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  7788,47:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  7790,22:            // Linear gradient
  7791,35:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  7791,44:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  7794,28:            const centerX = gradientCanvas.width / 2;
  7795,28:            const centerY = gradientCanvas.height / 2;
  7796,36:            const length = Math.max(gradientCanvas.width, gradientCanvas.height);
  7796,58:            const length = Math.max(gradientCanvas.width, gradientCanvas.height);
  7803,12:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  7803,23:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  7803,47:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  7807,20:        if (originalGradient.gradient.colors) {
  7807,29:        if (originalGradient.gradient.colors) {
  7808,20:            originalGradient.gradient.colors.forEach(colorStop => {
  7808,29:            originalGradient.gradient.colors.forEach(colorStop => {
  7809,16:                gradient.addColorStop(colorStop.position / 100, colorStop.color);
  7813,21:        // Fill with gradient
  7814,8:        gradientCtx.fillStyle = gradient;
  7814,32:        gradientCtx.fillStyle = gradient;
  7815,8:        gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);
  7815,35:        gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);
  7815,57:        gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);
  7817,33:        // Apply text as mask to gradient
  7818,8:        gradientCtx.globalCompositeOperation = 'destination-in';
  7819,8:        gradientCtx.drawImage(textCanvas, 0, 0);
  7826,24:        // Step 6: Draw gradient text on top
  7827,28:        targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);
  7827,45:        targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);
  7827,72:        targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);
  7829,54:        // Step 7: Apply decoration effects on top of gradient text
  7830,85:        console.log('🔍 RENDER ORDER: Step 7 - Applying decoration effects on top of gradient for Curved text');
  7895,84:            console.log('🎨 DECORATION: Applied decoration effects on top of curved gradient text');
  7924,28:        // Restore original gradient
  7925,12:        obj.gradient = originalGradient;
  7925,31:        obj.gradient = originalGradient;
  7927,24:        console.log('🎨 GRADIENT MASK: Curved text with gradient mask complete');
  7927,56:        console.log('🎨 GRADIENT MASK: Curved text with gradient mask complete');
  7930,7:    // Gradient masking function for grid distorted text
  7931,38:    function drawGridDistortedTextWithGradientMask(textObj, targetCtx, font) {
  7933,24:        console.log(`🎨 GRADIENT MASK [${callId}]: Drawing grid distorted text with gradient mask`);
  7933,84:        console.log(`🎨 GRADIENT MASK [${callId}]: Drawing grid distorted text with gradient mask`);
  7935,25:        // Save original gradient
  7936,22:        const originalGradient = textObj.gradient;
  7936,41:        const originalGradient = textObj.gradient;
  7938,41:        // IMPORTANT: Store the original gradient state for front outline detection
  7939,54:        // This must be done BEFORE modifying textObj.gradient
  7940,28:        textObj._originalHasGradient = originalGradient && originalGradient.type !== 'solid';
  7940,47:        textObj._originalHasGradient = originalGradient && originalGradient.type !== 'solid';
  7940,67:        textObj._originalHasGradient = originalGradient && originalGradient.type !== 'solid';
  7941,24:        console.log('🔍 GRADIENT MASK: Stored original gradient state:', textObj._originalHasGradient);
  7941,55:        console.log('🔍 GRADIENT MASK: Stored original gradient state:', textObj._originalHasGradient);
  7941,93:        console.log('🔍 GRADIENT MASK: Stored original gradient state:', textObj._originalHasGradient);
  7956,16:        textObj.gradient = { type: 'solid' };
  7974,45:        // Create a temporary object without gradient and effects for shape rendering
  7977,12:            gradient: { type: 'solid' },
  7987,26:        // Step 4: Create gradient canvas with high-DPI scaling
  7988,14:        const gradientCanvas = document.createElement('canvas');
  7989,8:        gradientCanvas.width = 2000 * scaleFactor;
  7990,8:        gradientCanvas.height = 2000 * scaleFactor;
  7991,14:        const gradientCtx = gradientCanvas.getContext('2d');
  7991,28:        const gradientCtx = gradientCanvas.getContext('2d');
  7992,8:        gradientCtx.scale(scaleFactor, scaleFactor);
  7993,8:        gradientCtx.imageSmoothingEnabled = true;
  7994,8:        gradientCtx.imageSmoothingQuality = 'high';
  7996,22:        // Create the gradient
  7997,14:        const gradientType = originalGradient.type;
  7997,37:        const gradientType = originalGradient.type;
  7998,12:        let gradient;
  8000,12:        if (gradientType === 'radial') {
  8001,28:            const centerX = gradientCanvas.width / 2;
  8002,28:            const centerY = gradientCanvas.height / 2;
  8003,36:            const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
  8003,58:            const radius = Math.max(gradientCanvas.width, gradientCanvas.height) / 3;
  8004,12:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  8004,23:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  8004,47:            gradient = gradientCtx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  8006,22:            // Linear gradient
  8007,35:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  8007,44:            const angle = (originalGradient.gradient.angle || 0) * Math.PI / 180;
  8010,28:            const centerX = gradientCanvas.width / 2;
  8011,28:            const centerY = gradientCanvas.height / 2;
  8012,36:            const length = Math.max(gradientCanvas.width, gradientCanvas.height);
  8012,58:            const length = Math.max(gradientCanvas.width, gradientCanvas.height);
  8019,12:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  8019,23:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  8019,47:            gradient = gradientCtx.createLinearGradient(x1, y1, x2, y2);
  8023,20:        if (originalGradient.gradient.colors) {
  8023,29:        if (originalGradient.gradient.colors) {
  8024,20:            originalGradient.gradient.colors.forEach(colorStop => {
  8024,29:            originalGradient.gradient.colors.forEach(colorStop => {
  8025,16:                gradient.addColorStop(colorStop.position / 100, colorStop.color);
  8029,21:        // Fill with gradient
  8030,8:        gradientCtx.fillStyle = gradient;
  8030,32:        gradientCtx.fillStyle = gradient;
  8031,8:        gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);
  8031,35:        gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);
  8031,57:        gradientCtx.fillRect(0, 0, gradientCanvas.width, gradientCanvas.height);
  8033,33:        // Apply text as mask to gradient
  8034,8:        gradientCtx.globalCompositeOperation = 'destination-in';
  8035,8:        gradientCtx.drawImage(textCanvas, 0, 0);
  8042,24:        // Step 6: Draw gradient text on top
  8043,28:        targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);
  8043,45:        targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);
  8043,72:        targetCtx.drawImage(gradientCanvas, -gradientCanvas.width / 2, -gradientCanvas.height / 2);
  8045,54:        // Step 7: Apply decoration effects on top of gradient text
  8046,37:        console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: Step 7 - Applying decoration effects on top of gradient for Grid Distort`);
  8046,106:        console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: Step 7 - Applying decoration effects on top of gradient for Grid Distort`);
  8047,37:        console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: Decoration mode: ${textObj.decorationMode}`);
  8048,37:        console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: Decoration module available: ${!!window.decorationModule}`);
  8051,41:            console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: ✅ PROCEEDING WITH DECORATION APPLICATION`);
  8059,41:            console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: Setting decoration type: ${textObj.decorationMode}`);
  8113,41:            console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: Calling decoration module with:`, {
  8123,41:            console.log(`🔍 GRID DISTORT GRADIENT [${callId}]: ✅ Applied decoration pattern for grid distort text: ${textObj.decorationMode}`);
  8145,90:            console.log('🎨 DECORATION: Applied decoration effects on top of grid distort gradient text');
  8156,111:            console.log(`🔍 FRONT OUTLINE SOURCE #4 [${callId}]: Drawing perspective shadow front outline from gradient masking (drawGridDistortedTextWithGradientMask)`);
  8156,154:            console.log(`🔍 FRONT OUTLINE SOURCE #4 [${callId}]: Drawing perspective shadow front outline from gradient masking (drawGridDistortedTextWithGradientMask)`);
  8174,28:        // Restore original gradient
  8175,16:        textObj.gradient = originalGradient;
  8175,35:        textObj.gradient = originalGradient;
  8177,34:        // Clean up the temporary gradient state flag
  8178,35:        delete textObj._originalHasGradient;
  8179,24:        console.log('🔍 GRADIENT MASK: Cleaned up original gradient state flag');
  8179,59:        console.log('🔍 GRADIENT MASK: Cleaned up original gradient state flag');
  8181,24:        console.log(`🎨 GRADIENT MASK [${callId}]: Grid distorted text with gradient mask complete`);
  8181,76:        console.log(`🎨 GRADIENT MASK [${callId}]: Grid distorted text with gradient mask complete`);
  8330,41:            // Check if we need to apply gradient masking
  8331,20:            if (obj.gradient && obj.gradient.type !== 'solid') {
  8331,36:            if (obj.gradient && obj.gradient.type !== 'solid') {
  8332,41:                drawGridDistortedTextWithGradientMask(obj, targetCtx, font);
  8357,41:            // Check if we need to apply gradient masking for fallback font too
  8358,20:            if (obj.gradient && obj.gradient.type !== 'solid') {
  8358,36:            if (obj.gradient && obj.gradient.type !== 'solid') {
  8359,41:                drawGridDistortedTextWithGradientMask(obj, targetCtx, regularFont);
  10231,44:        // Set text properties - let normal gradient system handle gradients
  10231,67:        // Set text properties - let normal gradient system handle gradients
  10497,49:            // Handle fill decoration (patterns, gradients, etc.)
  10504,62:        // Add front outlines for grid distorted text without gradients
  10505,103:        console.log('🔍 RENDER ORDER: Step 8 - Drawing front outlines on top for Grid Distort text (no gradient)');
  10507,116:            console.log('🔍 RENDER ORDER: Drawing perspective shadow front outline on top for Grid Distort text (no gradient)');
  10516,109:            console.log('🔍 RENDER ORDER: Drawing detailed 3D front outline on top for Grid Distort text (no gradient)');
  12096,40:        // Check if this is an SVG with gradient
  12097,30:        if (obj.fillType === 'gradient' && obj.gradient && obj.imageUrl && obj.imageUrl.toLowerCase().endsWith('.svg')) {
  12097,47:        if (obj.fillType === 'gradient' && obj.gradient && obj.imageUrl && obj.imageUrl.toLowerCase().endsWith('.svg')) {
  12098,45:            console.log('🎨 Drawing SVG with gradient:', obj.gradient);
  12098,61:            console.log('🎨 Drawing SVG with gradient:', obj.gradient);
  12100,33:            // Create bounds for gradient
  12108,50:            // Create an offscreen canvas for the gradient effect
  12114,24:            // Draw the gradient on the offscreen canvas
  12115,18:            const gradientBounds = {
  12121,20:            applySVGGradientFill(offscreenCtx, obj, gradientBounds);
  12121,52:            applySVGGradientFill(offscreenCtx, obj, gradientBounds);
  12136,45:                console.error("Error drawing gradient SVG:", e);
  14196,18:    // Initialize gradient color pickers
  14197,14:    initializeGradientColorPickers();

